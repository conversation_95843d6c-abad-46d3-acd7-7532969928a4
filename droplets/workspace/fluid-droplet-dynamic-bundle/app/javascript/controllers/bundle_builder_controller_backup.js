import { Controller } from "@hotwired/stimulus"

// Bundle Builder Controller for Rails 8
// Handles drag & drop product selection and bundle building
console.log("🔄 Bundle Builder Controller loading... " + new Date().toLocaleTimeString())

export default class extends Controller {
  static targets = [
    "categoryGrid",
    "productGrid",
    "dropZone",
    "selectedProducts",
    "emptyState",
    "productCount",
    "selectedCategoryName"
  ]
  static values = {
    selectedCategory: String,
    bundleProducts: Array,
    storeData: Object
  }

  connect() {
    console.log("🏗️ Bundle Builder Controller connected - " + new Date().toLocaleTimeString())
    console.log("Selected category:", this.selectedCategoryValue)
    console.log("Bundle products:", this.bundleProductsValue)
    console.log("Store data:", this.storeDataValue)

    // Debug targets
    console.log("🎯 Available targets:")
    console.log("  - dropZone:", this.hasDropZoneTarget, this.hasDropZoneTarget ? this.dropZoneTarget.id : 'N/A')
    console.log("  - selectedProducts:", this.hasSelectedProductsTarget, this.hasSelectedProductsTarget ? this.selectedProductsTarget.id : 'N/A')
    console.log("  - emptyState:", this.hasEmptyStateTarget, this.hasEmptyStateTarget ? this.emptyStateTarget.id : 'N/A')
    console.log("  - productGrid:", this.hasProductGridTarget, this.hasProductGridTarget ? this.productGridTarget.id : 'N/A')



    // Initialize with empty bundle products if not set
    if (!this.bundleProductsValue) {
      this.bundleProductsValue = []
    }

    // Initialize store data if not provided
    if (!this.storeDataValue || Object.keys(this.storeDataValue).length === 0) {
      this.storeDataValue = this.getDefaultStoreData()
    }

    this.initializeDragAndDrop()
    this.updateProductCount()
    this.updateEmptyState()

    // Load first category products automatically
    this.loadFirstCategory()
  }

  // DEBUG: Simple test method
  debugAddProduct() {
    console.log('🧪 DEBUG METHOD CALLED!')
    alert('Debug method is working!')

    const testProduct = {
      id: 'debug-' + Date.now(),
      name: 'Test Product',
      price: '$99.99',
      emoji: '🧪'
    }

    this.bundleProductsValue = [...this.bundleProductsValue, testProduct]
    this.renderSelectedProducts()
  }



  // Load the first category's products on initialization
  loadFirstCategory() {
    console.log('🎯 loadFirstCategory called')
    const firstCategory = this.categoryGridTarget.querySelector('.category-card.active')
    console.log('🎯 First category element:', firstCategory)

    if (firstCategory) {
      const categoryId = firstCategory.dataset.categoryId
      const categoryName = firstCategory.querySelector('.category-name').textContent
      console.log(`🎯 Auto-loading first category: ${categoryName} (ID: ${categoryId})`)
      console.log('🎯 Category products data:', firstCategory.dataset.products)

      this.selectedCategoryValue = categoryId
      this.loadCategoryProductsFromData(firstCategory)
      this.updateSelectedCategoryName(categoryName)
    } else {
      console.log('❌ No active category found, trying first category')
      const firstCategoryAny = this.categoryGridTarget.querySelector('.category-card')
      if (firstCategoryAny) {
        firstCategoryAny.classList.add('active')
        this.loadFirstCategory() // Retry
      }
    }
  }

  // Initialize drag and drop functionality
  initializeDragAndDrop() {
    console.log("🎯 Initializing drag and drop")

    // Setup drop zone first
    this.setupDropZone()

    // Setup product cards as draggable (will be called after products load)
    this.setupDraggableProducts()
  }

  // Setup product cards as draggable
  setupDraggableProducts() {
    const productCards = this.productGridTarget.querySelectorAll('.product-card[data-product]')
    console.log(`🎯 Setting up ${productCards.length} draggable products`)

    productCards.forEach(card => {
      // Skip if already initialized
      if (card.dataset.dragInitialized) return

      card.draggable = true
      card.dataset.dragInitialized = 'true'

      card.addEventListener('dragstart', (e) => {
        const productData = JSON.parse(card.dataset.product)
        console.log('🎯 Drag started:', productData.name)

        e.dataTransfer.setData('text/plain', JSON.stringify(productData))
        card.classList.add('dragging')

        // Store dragged product for drop handling
        this.draggedProduct = productData
      })

      card.addEventListener('dragend', (e) => {
        console.log('🎯 Drag ended')
        card.classList.remove('dragging')
        this.draggedProduct = null
      })
    })
  }

  // Setup drop zone for products
  setupDropZone() {
    if (!this.hasDropZoneTarget) {
      console.log('❌ Drop zone not found')
      return
    }

    const dropZone = this.dropZoneTarget

    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault()
      e.stopPropagation()
      dropZone.classList.add('drag-over')
      return false
    })

    dropZone.addEventListener('dragleave', (e) => {
      // Only remove drag-over if we're actually leaving the drop zone
      if (!dropZone.contains(e.relatedTarget)) {
        dropZone.classList.remove('drag-over')
      }
    })

    dropZone.addEventListener('drop', (e) => {
      e.preventDefault()
      e.stopPropagation()
      dropZone.classList.remove('drag-over')

      try {
        // Try to get product data from dataTransfer first
        let productData = null
        const transferData = e.dataTransfer.getData('text/plain')

        if (transferData) {
          productData = JSON.parse(transferData)
        } else if (this.draggedProduct) {
          productData = this.draggedProduct
        }

        if (productData) {
          console.log('🎯 Product dropped:', productData.name)
          this.addProductToBundle(productData)
        } else {
          console.log('❌ No product data found in drop event')
        }
      } catch (error) {
        console.error('❌ Error dropping product:', error)
      }

      return false
    })
  }

  // Handle category selection
  selectCategory(event) {
    console.log('🔥 selectCategory method called!')
    console.log('🔥 Event:', event)

    const categoryButton = event.currentTarget
    console.log('🔥 Category button:', categoryButton)
    console.log('🔥 Category button dataset:', categoryButton.dataset)
    console.log('🔥 Category button innerHTML:', categoryButton.innerHTML)

    const categoryId = categoryButton.dataset.categoryId
    const categoryNameElement = categoryButton.querySelector('.category-name')
    console.log('🔥 Category name element:', categoryNameElement)

    const categoryName = categoryNameElement ? categoryNameElement.textContent : 'Unknown'

    console.log(`📂 Category selected: ${categoryName} (${categoryId})`)

    // Update selected category
    this.selectedCategoryValue = categoryId

    // Update UI
    this.updateCategorySelection(categoryButton)
    this.updateSelectedCategoryName(categoryName)

    // Load products for this category from data attribute
    this.loadCategoryProductsFromData(categoryButton)
  }

  // Update category selection UI
  updateCategorySelection(selectedButton) {
    // Remove active class from all category buttons
    this.categoryGridTarget.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('active')
    })

    // Add active class to selected button
    selectedButton.classList.add('active')
  }

  // Update selected category name display
  updateSelectedCategoryName(categoryName) {
    if (this.hasSelectedCategoryNameTarget) {
      this.selectedCategoryNameTarget.textContent = categoryName
    }
  }

  // Load products for selected category from data attribute
  loadCategoryProductsFromData(categoryElement) {
    console.log(`📦 Loading products from category element`, categoryElement)
    console.log('📦 Category element dataset:', categoryElement.dataset)
    console.log('📦 All dataset keys:', Object.keys(categoryElement.dataset))

    try {
      // Get products from data-products attribute
      const productsJson = categoryElement.dataset.products
      console.log('📦 Raw products JSON:', productsJson)
      console.log('📦 Products JSON type:', typeof productsJson)
      console.log('📦 Products JSON length:', productsJson ? productsJson.length : 'null')
      console.log('📦 First 100 chars of JSON:', productsJson ? productsJson.substring(0, 100) : 'null')

      let products = []
      if (productsJson && productsJson !== 'null' && productsJson !== '[]' && productsJson.trim() !== '') {
        console.log('📦 Attempting to parse JSON...')
        products = JSON.parse(productsJson)
        console.log('📦 Successfully parsed products:', products)
        console.log('📦 First product:', products[0])
      } else {
        console.log('📦 No products JSON or empty/null value')
        console.log('📦 Condition checks:')
        console.log('  - productsJson exists:', !!productsJson)
        console.log('  - not "null":', productsJson !== 'null')
        console.log('  - not "[]":', productsJson !== '[]')
        console.log('  - not empty after trim:', productsJson ? productsJson.trim() !== '' : false)
      }

      console.log('📦 Final products array:', products)
      console.log('📦 Products count:', products.length)
      console.log('📦 About to call renderProducts...')

      this.renderProducts(products)
    } catch (error) {
      console.error('❌ Error parsing products:', error)
      console.error('❌ Products JSON was:', categoryElement.dataset.products)
      console.error('❌ Error stack:', error.stack)
      this.renderProducts([])
    }
  }

  // Load products for selected category (legacy method)
  loadCategoryProducts(categoryKey) {
    console.log(`📦 Loading products for category: ${categoryKey}`)

    // Get products from store data
    const products = this.storeDataValue[categoryKey] || []
    console.log('📦 Products found:', products)

    this.renderProducts(products)
  }

  // Render products from category data
  renderProducts(products) {
    console.log('🎨 renderProducts called with:', products)
    console.log('🎨 Products type:', typeof products)
    console.log('🎨 Products is array:', Array.isArray(products))
    console.log('🎨 productGridTarget:', this.productGridTarget)

    // Show loading state briefly
    this.productGridTarget.innerHTML = `
      <div class="loading-products text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading products...</span>
        </div>
        <p class="mt-2 text-muted">Loading products...</p>
      </div>
    `

    console.log('🎨 Loading state set, starting timeout...')

    // Simulate loading delay for better UX
    setTimeout(() => {
      console.log('🎨 Timeout executed, rendering products...')

      if (products && products.length > 0) {
        console.log(`🎨 Rendering ${products.length} products in grid`)
        console.log('🎨 First product:', products[0])

        const productHTML = products.map(product => `
          <div class="col-6">
            <div class="category-card product-card"
                 draggable="true"
                 data-product-id="${product.id}"
                 data-product-name="${product.name}"
                 data-product-price="${product.price}"
                 data-product-emoji="${product.emoji}"
                 data-product-image="${product.image_url || ''}"
                 data-action="dragstart->bundle-builder#handleDragStart dragend->bundle-builder#handleDragEnd">
              <div class="product-image mb-1">
                ${product.image_url ?
                  `<img src="${product.image_url}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">` :
                  `<span style="font-size: 1rem;">${product.emoji || '📦'}</span>`
                }
              </div>
              <div class="product-name">${product.name}</div>
              <div class="product-price">${product.price}</div>
            </div>
          </div>
        `).join('')

        console.log('🎨 Generated HTML length:', productHTML.length)
        console.log('🎨 Generated HTML:', productHTML)
        this.productGridTarget.innerHTML = productHTML
        console.log('✅ Products rendered with drag actions in 2x2 grid')
      } else {
        console.log('🎨 No products, showing empty state')
        this.productGridTarget.innerHTML = `
          <div class="col-12">
            <div class="text-center py-4 text-muted">
              <div class="empty-icon">📦</div>
              <div>No products in this category</div>
            </div>
          </div>
        `
        console.log('✅ Empty state rendered')
      }

      // Re-initialize drag and drop for new products
      console.log('🎨 Re-initializing drag and drop...')
      this.setupDraggableProducts()
      console.log('🎨 Drag and drop setup complete')
    }, 300)
  }

  // Add product to bundle
  addProductToBundle(product) {
    console.log('🎯 Adding product to bundle:', product)
    console.log('📦 Current bundle products:', this.bundleProductsValue)
    console.log('🎯 selectedProducts target exists:', this.hasSelectedProductsTarget)
    console.log('🎯 selectedProducts target element:', this.selectedProductsTarget)

    // Check if product already exists
    if (this.bundleProductsValue.some(p => p.id == product.id)) {
      console.log('⚠️ Product already in bundle')
      this.showNotification(`${product.name} is already in the bundle!`, 'warning')
      return
    }

    // Add to bundle products array
    console.log('➕ Adding product to array...')
    this.bundleProductsValue = [...this.bundleProductsValue, product]
    console.log('📦 New bundle products:', this.bundleProductsValue)
    console.log('📦 Bundle products length:', this.bundleProductsValue.length)

    // Update UI
    console.log('🎨 Updating UI...')
    console.log('🎨 About to call renderSelectedProducts...')
    this.renderSelectedProducts()
    console.log('🎨 renderSelectedProducts called, now updating count...')
    this.updateProductCount()
    console.log('🎨 updateProductCount called, now updating empty state...')
    this.updateEmptyState()
    console.log('🎨 All UI updates completed')

    // Show success notification
    this.showNotification(`${product.name} added to bundle!`, 'success')
    console.log('✅ Product added to bundle successfully:', product.name)
  }

  // Remove product from bundle
  removeProduct(event) {
    const productId = event.currentTarget.dataset.productId
    console.log(`🗑️ Removing product: ${productId}`)

    // Remove from bundle products array
    this.bundleProductsValue = this.bundleProductsValue.filter(p => p.id != productId)

    // Update UI
    this.renderSelectedProducts()
    this.updateProductCount()
    this.updateEmptyState()

    this.showNotification('Product removed from bundle', 'info')
  }



  // Render selected products in the bundle
  renderSelectedProducts() {
    console.log('🎨 renderSelectedProducts called')
    console.log('🎯 Has target:', this.hasSelectedProductsTarget)
    console.log('📦 Products to render:', this.bundleProductsValue.length)

    if (!this.hasSelectedProductsTarget) {
      console.log('❌ No selectedProducts target found!')
      return
    }

    const gridContainer = this.selectedProductsTarget.querySelector('.row')
    if (!gridContainer) {
      console.log('❌ No grid container found in selectedProducts target')
      return
    }

    if (this.bundleProductsValue.length === 0) {
      console.log('📦 No products, clearing container')
      gridContainer.innerHTML = ''
      return
    }

    console.log('🎨 Generating HTML for products...')

    // Use the same pattern as the product list
    const html = this.bundleProductsValue.map(product => `
      <div class="col-6 col-md-4 col-lg-3">
        <div class="product-card" style="position: relative;">
          <div class="product-image">
            ${product.image_url ?
              `<img src="${product.image_url}" alt="${product.name}" style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;">` :
              `<span style="font-size: 1.2rem;">${product.emoji || '📦'}</span>`
            }
          </div>
          <div class="product-info">
            <div class="product-name">${product.name}</div>
            <div class="product-price">${product.price}</div>
          </div>
          <button type="button"
                  class="btn btn-sm btn-danger"
                  style="position: absolute; top: 5px; right: 5px; width: 20px; height: 20px; padding: 0; font-size: 12px; line-height: 1;"
                  data-action="click->bundle-builder#removeProduct"
                  data-product-id="${product.id}">
            ✕
          </button>
        </div>
      </div>
    `).join('')

    console.log('🎨 Setting HTML to grid container')
    gridContainer.innerHTML = html
    console.log('✅ Products rendered successfully')
  }

  // Update product count display
  updateProductCount() {
    if (this.hasProductCountTarget) {
      const count = this.bundleProductsValue.length
      this.productCountTarget.textContent = `${count} product${count !== 1 ? 's' : ''} selected`
    }
  }

  // Update empty state visibility
  updateEmptyState() {
    console.log('👻 Updating empty state, products count:', this.bundleProductsValue.length)
    console.log('🎯 Has emptyState target:', this.hasEmptyStateTarget)
    console.log('🎯 Has selectedProducts target:', this.hasSelectedProductsTarget)

    // The CSS now handles visibility automatically based on :empty and :not(:empty) selectors
    // We just need to make sure the containers exist
    if (this.hasSelectedProductsTarget) {
      console.log('👻 Selected products container innerHTML length:', this.selectedProductsTarget.innerHTML.length)
    }

    if (this.hasEmptyStateTarget) {
      console.log('👻 Empty state element display:', window.getComputedStyle(this.emptyStateTarget).display)
    }
  }

  // Show notification to user
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div')
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' :
                      type === 'error' ? 'alert-danger' : 'alert-info'

    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;'
    notification.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `

    document.body.appendChild(notification)

    // Auto remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove()
      }
    }, 3000)
  }

  // Get default store data for development
  getDefaultStoreData() {
    return {
      supplements: [
        { id: 1, name: 'Vitamin D3', price: 24.99, emoji: '☀️' },
        { id: 2, name: 'Omega-3', price: 32.99, emoji: '🐟' },
        { id: 3, name: 'Probiotics', price: 28.99, emoji: '🦠' },
        { id: 4, name: 'Magnesium', price: 19.99, emoji: '⚡' }
      ],
      nutrition: [
        { id: 5, name: 'Protein Powder', price: 45.99, emoji: '💪' },
        { id: 6, name: 'Green Smoothie', price: 38.99, emoji: '🥬' },
        { id: 7, name: 'Energy Bars', price: 24.99, emoji: '🍫' },
        { id: 8, name: 'Meal Replacement', price: 52.99, emoji: '🥤' }
      ],
      fitness: [
        { id: 9, name: 'Pre-Workout', price: 34.99, emoji: '🔥' },
        { id: 10, name: 'BCAA', price: 29.99, emoji: '💊' },
        { id: 11, name: 'Creatine', price: 22.99, emoji: '⚡' },
        { id: 12, name: 'Recovery Drink', price: 31.99, emoji: '🧊' }
      ],
      wellness: [
        { id: 13, name: 'Meditation App', price: 9.99, emoji: '🧘' },
        { id: 14, name: 'Sleep Support', price: 26.99, emoji: '😴' },
        { id: 15, name: 'Stress Relief', price: 23.99, emoji: '🌿' },
        { id: 16, name: 'Immunity Boost', price: 35.99, emoji: '🛡️' }
      ]
    }
  }

  // Stimulus drag event handlers
  handleDragStart(event) {
    const element = event.currentTarget
    const productData = {
      id: element.dataset.productId,
      name: element.dataset.productName,
      price: element.dataset.productPrice,
      emoji: element.dataset.productEmoji,
      image_url: element.dataset.productImage
    }
    console.log('🎯 Drag started:', productData.name)

    event.dataTransfer.setData('text/plain', JSON.stringify(productData))
    element.classList.add('dragging')

    // Store dragged product for drop handling
    this.draggedProduct = productData
  }

  handleDragEnd(event) {
    console.log('🎯 Drag ended')
    event.currentTarget.classList.remove('dragging')
    this.draggedProduct = null
  }

  // Handle drop zone events (these might be called from HTML data-actions too)
  handleDragOver(event) {
    event.preventDefault()
    event.stopPropagation()
    this.dropZoneTarget.classList.add('drag-over')
    return false
  }

  handleDragLeave(event) {
    // Only remove drag-over if we're actually leaving the drop zone
    if (!this.dropZoneTarget.contains(event.relatedTarget)) {
      this.dropZoneTarget.classList.remove('drag-over')
    }
  }

  handleDrop(event) {
    event.preventDefault()
    event.stopPropagation()
    this.dropZoneTarget.classList.remove('drag-over')

    try {
      // Try to get product data from dataTransfer first
      let productData = null
      const transferData = event.dataTransfer.getData('text/plain')

      if (transferData) {
        productData = JSON.parse(transferData)
      } else if (this.draggedProduct) {
        productData = this.draggedProduct
      }

      if (productData) {
        console.log('🎯 Product dropped:', productData.name)
        this.addProductToBundle(productData)
      } else {
        console.log('❌ No product data found in drop event')
      }
    } catch (error) {
      console.error('❌ Error handling drop:', error)
    }

    return false
  }
}
