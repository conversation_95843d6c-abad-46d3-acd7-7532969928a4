import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "categoryGrid",
    "productGrid",
    "dropZone",
    "emptyState",
    "selectedProducts",
    "productCount",
    "productCountBadge",
    "searchInput",
    "saveButton",
    "form",
    "bundleDataInput"
  ]

  static values = {
    selectedCategory: String,
    bundleProducts: Array
  }

  connect() {
    console.log("🏗️ Bundle Builder Controller connected!")

    // Initialize data
    this.bundleProductsValue = this.bundleProductsValue || []
    this.selectedCategoryValue = this.selectedCategoryValue || ""

    // Setup drag and drop
    this.setupDragAndDrop()

    // Load initial products from first category
    this.loadInitialProducts()

    // Update UI
    this.updateUI()
  }

  // Load products from the first active category
  loadInitialProducts() {
    const firstActiveCategory = this.categoryGridTarget.querySelector('.category-card.active')
    if (firstActiveCategory) {
      this.loadProductsFromCategory(firstActiveCategory)
    }
  }

  // Handle category selection
  selectCategory(event) {
    const categoryCard = event.currentTarget

    // Update active category
    this.categoryGridTarget.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('active')
    })
    categoryCard.classList.add('active')

    // Load products for this category
    this.loadProductsFromCategory(categoryCard)
  }

  // Load products from category data
  loadProductsFromCategory(categoryCard) {
    const categoryId = categoryCard.dataset.categoryId
    const categoryName = categoryCard.dataset.categoryName
    const productsData = categoryCard.dataset.products

    console.log(`Loading products for category: ${categoryName}`)

    try {
      const products = JSON.parse(productsData)
      this.renderProducts(products)
    } catch (error) {
      console.error('Error parsing products data:', error)
      this.showProductsError()
    }
  }

  // Render products in the grid
  renderProducts(products) {
    if (!products || products.length === 0) {
      this.showNoProducts()
      return
    }

    const productsHTML = products.map(product => `
      <div class="col-6 col-md-4 col-lg-3">
        <div class="product-card"
             draggable="true"
             data-product-id="${product.id}"
             data-product-data='${JSON.stringify(product)}'
             data-action="dragstart->bundle-builder#handleDragStart dragend->bundle-builder#handleDragEnd click->bundle-builder#addProduct">
          <div class="product-image">
            ${product.image_url ?
              `<img src="${product.image_url}" alt="${product.title}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;">` :
              `<span style="font-size: 1.5rem;">📦</span>`
            }
          </div>
          <div class="product-name">${product.title}</div>
          <div class="product-price">$${parseFloat(product.price || 0).toFixed(2)}</div>
        </div>
      </div>
    `).join('')

    this.productGridTarget.innerHTML = productsHTML
  }

  // Show no products message
  showNoProducts() {
    this.productGridTarget.innerHTML = `
      <div class="col-12">
        <div class="text-center py-4 text-muted">
          <div class="mb-2">📦</div>
          <div>No products found in this category</div>
        </div>
      </div>
    `
  }

  // Show products error
  showProductsError() {
    this.productGridTarget.innerHTML = `
      <div class="col-12">
        <div class="text-center py-4 text-danger">
          <div class="mb-2">⚠️</div>
          <div>Error loading products</div>
        </div>
      </div>
    `
  }

  // Debug method to add a test product
  debugAddProduct() {
    const testProduct = {
      id: Date.now(),
      title: "Test Product",
      price: "19.99",
      image_url: null
    }

    console.log("🧪 Adding test product:", testProduct)
    this.addProductToBundle(testProduct)
  }

  // Setup drag and drop functionality
  setupDragAndDrop() {
    if (this.hasDropZoneTarget) {
      this.dropZoneTarget.addEventListener('dragover', this.handleDragOver.bind(this))
      this.dropZoneTarget.addEventListener('dragleave', this.handleDragLeave.bind(this))
      this.dropZoneTarget.addEventListener('drop', this.handleDrop.bind(this))
    }
  }

  // Handle drag start
  handleDragStart(event) {
    const productData = event.currentTarget.dataset.productData

    if (productData) {
      event.dataTransfer.setData('text/plain', productData)
      event.currentTarget.classList.add('dragging')
      console.log("🎯 Drag started for product")
    }
  }

  // Handle drag end
  handleDragEnd(event) {
    event.currentTarget.classList.remove('dragging')
  }

  // Handle drag over
  handleDragOver(event) {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
    this.dropZoneTarget.classList.add('drag-over')
  }

  // Handle drag leave
  handleDragLeave(event) {
    // Only remove drag-over if we're actually leaving the drop zone
    if (!this.dropZoneTarget.contains(event.relatedTarget)) {
      this.dropZoneTarget.classList.remove('drag-over')
    }
  }

  // Handle drop
  handleDrop(event) {
    event.preventDefault()
    this.dropZoneTarget.classList.remove('drag-over')

    try {
      const productData = JSON.parse(event.dataTransfer.getData('text/plain'))
      console.log("📦 Product dropped:", productData)
      this.addProductToBundle(productData)
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  // Add product to bundle (via click or drag)
  addProduct(event) {
    const productData = event.currentTarget.dataset.productData

    if (productData) {
      try {
        const product = JSON.parse(productData)
        console.log("👆 Product clicked:", product)
        this.addProductToBundle(product)
      } catch (error) {
        console.error('Error parsing product data:', error)
      }
    }
  }

  // Add product to bundle
  addProductToBundle(product) {
    // Check if product already exists
    if (this.bundleProductsValue.some(p => p.id.toString() === product.id.toString())) {
      console.log('Product already in bundle')
      return
    }

    // Add to bundle
    this.bundleProductsValue = [...this.bundleProductsValue, product]
    console.log(`Added ${product.title} to bundle`)

    // Update UI
    this.updateUI()
  }

  // Remove product from bundle
  removeProduct(event) {
    const productId = event.currentTarget.dataset.productId
    this.bundleProductsValue = this.bundleProductsValue.filter(
      p => p.id.toString() !== productId
    )

    console.log(`Removed product ${productId} from bundle`)
    this.updateUI()
  }

  // Clear all products from bundle
  clearBundle() {
    if (this.bundleProductsValue.length === 0) return

    if (confirm('Are you sure you want to clear all products from the bundle?')) {
      this.bundleProductsValue = []
      this.updateUI()
    }
  }

  // Update UI based on current state
  updateUI() {
    this.updateProductCount()
    this.updateSelectedProducts()
    this.updateSaveButton()
    this.updateFormData()
  }

  // Update product count displays
  updateProductCount() {
    const count = this.bundleProductsValue.length
    const text = `${count} product${count !== 1 ? 's' : ''}`

    if (this.hasProductCountTarget) {
      this.productCountTarget.textContent = text
    }

    if (this.hasProductCountBadgeTarget) {
      this.productCountBadgeTarget.textContent = text
    }
  }

  // Update selected products display
  updateSelectedProducts() {
    const hasProducts = this.bundleProductsValue.length > 0

    // Show/hide empty state
    if (this.hasEmptyStateTarget) {
      this.emptyStateTarget.style.display = hasProducts ? 'none' : 'block'
    }

    // Show/hide selected products
    if (this.hasSelectedProductsTarget) {
      this.selectedProductsTarget.style.display = hasProducts ? 'grid' : 'none'

      if (hasProducts) {
        this.selectedProductsTarget.innerHTML = this.renderSelectedProducts()
      }
    }
  }

  // Render selected products
  renderSelectedProducts() {
    return this.bundleProductsValue.map(product => `
      <div class="bundle-product-card">
        <div class="bundle-product-image">
          ${product.image_url ?
            `<img src="${product.image_url}" alt="${product.title}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">` :
            `<span style="font-size: 1.5rem;">📦</span>`
          }
        </div>
        <div class="bundle-product-info">
          <div class="bundle-product-name">${product.title}</div>
          <div class="bundle-product-price">$${parseFloat(product.price || 0).toFixed(2)}</div>
        </div>
        <button type="button"
                class="bundle-product-remove"
                data-product-id="${product.id}"
                data-action="click->bundle-builder#removeProduct"
                title="Remove product">
          ×
        </button>
      </div>
    `).join('')
  }

  // Update save button state
  updateSaveButton() {
    if (this.hasSaveButtonTarget) {
      this.saveButtonTarget.disabled = this.bundleProductsValue.length === 0
    }
  }

  // Update form data for submission
  updateFormData() {
    if (this.hasBundleDataInputTarget) {
      const bundleData = {
        products: this.bundleProductsValue.map(product => ({
          id: product.id,
          title: product.title,
          price: product.price,
          sku: product.sku
        }))
      }

      this.bundleDataInputTarget.value = JSON.stringify(bundleData)
    }
  }
}
