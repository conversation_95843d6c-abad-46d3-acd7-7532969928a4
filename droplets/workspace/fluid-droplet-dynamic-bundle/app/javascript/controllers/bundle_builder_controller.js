import { Controller } from "@hotwired/stimulus"

console.log("🔄 NEW Bundle Builder Controller loading...")

export default class extends Controller {
  static targets = ["dropZone", "selectedProducts", "emptyState", "productGrid", "productCount"]
  static values = { 
    selectedCategory: String,
    bundleProducts: Array,
    storeData: Object
  }

  connect() {
    console.log("🏗️ NEW Bundle Builder Controller connected!")

    // Initialize with empty bundle products if not set
    if (!this.bundleProductsValue) {
      this.bundleProductsValue = []
    }

    this.initializeDragAndDrop()
    this.renderSelectedProducts()
    this.loadFirstCategory()
  }

  // DEBUG: Simple test method
  debugAddProduct() {
    console.log('🧪 DEBUG METHOD CALLED!')
    alert('Debug method is working!')
    
    const testProduct = {
      id: 'debug-' + Date.now(),
      name: 'Test Product',
      price: '$99.99',
      emoji: '🧪'
    }
    
    this.bundleProductsValue = [...this.bundleProductsValue, testProduct]
    this.renderSelectedProducts()
  }

  // Initialize drag and drop functionality
  initializeDragAndDrop() {
    console.log('🎯 Initializing drag and drop...')
    
    // Setup drop zone
    if (this.hasDropZoneTarget) {
      this.dropZoneTarget.addEventListener('dragover', this.handleDragOver.bind(this))
      this.dropZoneTarget.addEventListener('drop', this.handleDrop.bind(this))
    }
  }

  // Handle drag over event
  handleDragOver(event) {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }

  // Handle drop event
  handleDrop(event) {
    event.preventDefault()
    console.log('🎯 Drop event triggered')
    
    try {
      const productDataString = event.dataTransfer.getData('text/plain')
      console.log('📦 Dropped data:', productDataString)
      
      if (productDataString) {
        const productData = JSON.parse(productDataString)
        console.log('🎯 Product dropped:', productData.name)
        this.addProductToBundle(productData)
      }
    } catch (error) {
      console.error('❌ Error dropping product:', error)
    }
  }

  // Add product to bundle
  addProductToBundle(product) {
    console.log('🎯 Adding product to bundle:', product)

    // Check if product already exists
    if (this.bundleProductsValue.some(p => p.id == product.id)) {
      console.log('⚠️ Product already in bundle')
      return
    }

    // Add to bundle products array
    this.bundleProductsValue = [...this.bundleProductsValue, product]
    console.log('📦 New bundle products:', this.bundleProductsValue)
    console.log('📦 Bundle now has', this.bundleProductsValue.length, 'products')

    // Update UI
    console.log('🔄 About to call renderSelectedProducts...')
    this.renderSelectedProducts()
    console.log('🔄 renderSelectedProducts call completed')
  }

  // Remove product from bundle
  removeProduct(event) {
    const productId = event.currentTarget.dataset.productId
    console.log(`🗑️ Removing product: ${productId}`)

    // Remove from bundle products array
    this.bundleProductsValue = this.bundleProductsValue.filter(p => p.id != productId)

    // Update UI
    this.renderSelectedProducts()
  }

  // Render selected products in the bundle
  renderSelectedProducts() {
    console.log('🎨 renderSelectedProducts called')
    console.log('🎯 Has target:', this.hasSelectedProductsTarget)
    console.log('📦 Products to render:', this.bundleProductsValue.length)
    console.log('📦 Products array:', this.bundleProductsValue)

    if (!this.hasSelectedProductsTarget) {
      console.log('❌ No selectedProducts target found!')
      return
    }

    console.log('🎯 selectedProducts target element:', this.selectedProductsTarget)
    console.log('🎯 selectedProducts target innerHTML before:', this.selectedProductsTarget.innerHTML)

    const gridContainer = this.selectedProductsTarget.querySelector('.row')
    console.log('🎯 Grid container found:', !!gridContainer)
    console.log('🎯 Grid container element:', gridContainer)

    if (!gridContainer) {
      console.log('❌ No grid container found in selectedProducts target')
      console.log('❌ selectedProducts innerHTML:', this.selectedProductsTarget.innerHTML)
      return
    }

    if (this.bundleProductsValue.length === 0) {
      console.log('📦 No products, clearing container')
      gridContainer.innerHTML = ''
      return
    }

    console.log('🎨 Generating HTML for products...')

    // Use the same pattern as the product list
    const html = this.bundleProductsValue.map(product => `
      <div class="col-6 col-md-4 col-lg-3">
        <div class="product-card" style="position: relative;">
          <div class="product-image">
            ${product.image_url ?
              `<img src="${product.image_url}" alt="${product.name}" style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;">` :
              `<span style="font-size: 1.2rem;">${product.emoji || '📦'}</span>`
            }
          </div>
          <div class="product-info">
            <div class="product-name">${product.name}</div>
            <div class="product-price">${product.price}</div>
          </div>
          <button type="button" 
                  class="btn btn-sm btn-danger"
                  style="position: absolute; top: 5px; right: 5px; width: 20px; height: 20px; padding: 0; font-size: 12px; line-height: 1;"
                  data-action="click->bundle-builder#removeProduct"
                  data-product-id="${product.id}">
            ✕
          </button>
        </div>
      </div>
    `).join('')

    console.log('🎨 Setting HTML to grid container')
    gridContainer.innerHTML = html
    console.log('✅ Products rendered successfully')
  }

  // Load first category products automatically
  loadFirstCategory() {
    console.log('📦 Loading first category products...')

    // Generate test products for now
    const testProducts = [
      {
        id: 'apple-watch-ultra',
        name: 'Apple Watch Ultra',
        price: '$0.00',
        emoji: '⌚',
        image_url: 'https://images.unsplash.com/photo-1434493789847-2f02dc6ca35d?w=100&h=100&fit=crop'
      },
      {
        id: 'protein-powder',
        name: 'Protein Powder',
        price: '$29.99',
        emoji: '🥤',
        image_url: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=100&h=100&fit=crop'
      },
      {
        id: 'vitamins',
        name: 'Daily Vitamins',
        price: '$19.99',
        emoji: '💊',
        image_url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=100&h=100&fit=crop'
      }
    ]

    this.renderProducts(testProducts)
  }

  // Render products in the grid
  renderProducts(products) {
    if (!this.hasProductGridTarget) {
      console.log('❌ No product grid target found')
      return
    }

    const html = products.map(product => `
      <div class="col-6 col-md-4 col-lg-3">
        <div class="product-card"
             draggable="true"
             data-product-id="${product.id}"
             data-action="dragstart->bundle-builder#handleDragStart">
          <div class="product-image">
            ${product.image_url ?
              `<img src="${product.image_url}" alt="${product.name}" style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;">` :
              `<span style="font-size: 1.2rem;">${product.emoji || '📦'}</span>`
            }
          </div>
          <div class="product-info">
            <div class="product-name">${product.name}</div>
            <div class="product-price">${product.price}</div>
          </div>
        </div>
      </div>
    `).join('')

    this.productGridTarget.innerHTML = html
    console.log('✅ Products rendered in grid')
  }

  // Handle drag start for products
  handleDragStart(event) {
    const productCard = event.currentTarget
    const productId = productCard.dataset.productId

    console.log('🎯 Drag started for product:', productId)

    // Find the product data
    const testProducts = [
      {
        id: 'apple-watch-ultra',
        name: 'Apple Watch Ultra',
        price: '$0.00',
        emoji: '⌚',
        image_url: 'https://images.unsplash.com/photo-1434493789847-2f02dc6ca35d?w=100&h=100&fit=crop'
      },
      {
        id: 'protein-powder',
        name: 'Protein Powder',
        price: '$29.99',
        emoji: '🥤',
        image_url: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=100&h=100&fit=crop'
      },
      {
        id: 'vitamins',
        name: 'Daily Vitamins',
        price: '$19.99',
        emoji: '💊',
        image_url: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=100&h=100&fit=crop'
      }
    ]

    const productData = testProducts.find(p => p.id === productId)

    if (productData) {
      event.dataTransfer.setData('text/plain', JSON.stringify(productData))
      console.log('📦 Product data set for drag:', productData.name)
    }
  }
}
