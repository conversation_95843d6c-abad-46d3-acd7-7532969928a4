import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "categoriesGrid", 
    "productsGrid", 
    "dropZone", 
    "emptyState", 
    "selectedProducts",
    "productCount",
    "productCountBadge",
    "searchInput",
    "saveButton",
    "form",
    "bundleDataInput"
  ]
  
  static values = { 
    availableProducts: Array,
    bundleProducts: Array
  }

  connect() {
    console.log("🏗️ Bundle Builder Controller connected!")
    
    // Initialize data
    this.bundleProductsValue = this.bundleProductsValue || []
    this.currentCategory = 'all'
    
    // Load initial data
    this.loadCategories()
    this.loadProducts()
    this.updateUI()
    
    // Setup drag and drop
    this.setupDragAndDrop()
  }

  // Load categories from available products
  loadCategories() {
    const categories = this.extractCategories()
    const categoriesHTML = this.renderCategories(categories)
    this.categoriesGridTarget.innerHTML = categoriesHTML
  }

  // Extract unique categories from products
  extractCategories() {
    const categoryMap = new Map()
    
    // Add "All" category
    categoryMap.set('all', {
      id: 'all',
      name: 'All Products',
      icon: '🏪',
      count: this.availableProductsValue.length
    })
    
    // Extract categories from products
    this.availableProductsValue.forEach(product => {
      const category = product.product_type || 'other'
      if (!categoryMap.has(category)) {
        categoryMap.set(category, {
          id: category,
          name: this.formatCategoryName(category),
          icon: this.getCategoryIcon(category),
          count: 0
        })
      }
      categoryMap.get(category).count++
    })
    
    return Array.from(categoryMap.values())
  }

  // Format category name for display
  formatCategoryName(category) {
    return category.charAt(0).toUpperCase() + category.slice(1).replace(/_/g, ' ')
  }

  // Get icon for category
  getCategoryIcon(category) {
    const icons = {
      'supplement': '💊',
      'nutrition': '🥗',
      'fitness': '💪',
      'wellness': '🧘',
      'other': '📦'
    }
    return icons[category] || '📦'
  }

  // Render categories HTML
  renderCategories(categories) {
    return categories.map(category => `
      <div class="category-card ${category.id === this.currentCategory ? 'active' : ''}"
           data-category="${category.id}"
           data-action="click->bundle-builder#selectCategory">
        <div class="category-icon">${category.icon}</div>
        <div class="category-name">${category.name}</div>
        <div class="category-count">${category.count} items</div>
      </div>
    `).join('')
  }

  // Load and display products
  loadProducts() {
    const filteredProducts = this.getFilteredProducts()
    const productsHTML = this.renderProducts(filteredProducts)
    this.productsGridTarget.innerHTML = productsHTML
  }

  // Get filtered products based on current category and search
  getFilteredProducts() {
    let products = this.availableProductsValue
    
    // Filter by category
    if (this.currentCategory !== 'all') {
      products = products.filter(product => 
        (product.product_type || 'other') === this.currentCategory
      )
    }
    
    // Filter by search term
    const searchTerm = this.hasSearchInputTarget ? 
      this.searchInputTarget.value.toLowerCase() : ''
    
    if (searchTerm) {
      products = products.filter(product =>
        product.title.toLowerCase().includes(searchTerm) ||
        (product.description && product.description.toLowerCase().includes(searchTerm))
      )
    }
    
    return products
  }

  // Render products HTML
  renderProducts(products) {
    return products.map(product => `
      <div class="product-card"
           draggable="true"
           data-product-id="${product.id}"
           data-action="dragstart->bundle-builder#handleDragStart click->bundle-builder#addProduct">
        <div class="product-image">
          ${product.image_url ? 
            `<img src="${product.image_url}" alt="${product.title}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">` :
            `<span style="font-size: 1.5rem;">📦</span>`
          }
        </div>
        <div class="product-name">${product.title}</div>
        <div class="product-price">$${parseFloat(product.price || 0).toFixed(2)}</div>
      </div>
    `).join('')
  }

  // Handle category selection
  selectCategory(event) {
    const categoryId = event.currentTarget.dataset.category
    this.currentCategory = categoryId
    
    // Update active category
    this.categoriesGridTarget.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('active')
    })
    event.currentTarget.classList.add('active')
    
    // Reload products
    this.loadProducts()
  }

  // Handle search
  searchProducts(event) {
    this.loadProducts()
  }

  // Setup drag and drop functionality
  setupDragAndDrop() {
    // Prevent default drag behavior on drop zone
    if (this.hasDropZoneTarget) {
      this.dropZoneTarget.addEventListener('dragover', this.handleDragOver.bind(this))
      this.dropZoneTarget.addEventListener('drop', this.handleDrop.bind(this))
    }
  }

  // Handle drag start
  handleDragStart(event) {
    const productId = event.currentTarget.dataset.productId
    const product = this.availableProductsValue.find(p => p.id.toString() === productId)
    
    if (product) {
      event.dataTransfer.setData('text/plain', JSON.stringify(product))
      event.currentTarget.style.opacity = '0.5'
    }
  }

  // Handle drag over
  handleDragOver(event) {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
    this.dropZoneTarget.classList.add('drag-over')
  }

  // Handle drag leave
  handleDragLeave(event) {
    this.dropZoneTarget.classList.remove('drag-over')
  }

  // Handle drop
  handleDrop(event) {
    event.preventDefault()
    this.dropZoneTarget.classList.remove('drag-over')
    
    try {
      const productData = JSON.parse(event.dataTransfer.getData('text/plain'))
      this.addProductToBundle(productData)
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  // Add product to bundle (via click or drag)
  addProduct(event) {
    const productId = event.currentTarget.dataset.productId
    const product = this.availableProductsValue.find(p => p.id.toString() === productId)
    
    if (product) {
      this.addProductToBundle(product)
    }
  }

  // Add product to bundle
  addProductToBundle(product) {
    // Check if product already exists
    if (this.bundleProductsValue.some(p => p.id.toString() === product.id.toString())) {
      console.log('Product already in bundle')
      return
    }
    
    // Add to bundle
    this.bundleProductsValue = [...this.bundleProductsValue, product]
    console.log(`Added ${product.title} to bundle`)
    
    // Update UI
    this.updateUI()
  }

  // Remove product from bundle
  removeProduct(event) {
    const productId = event.currentTarget.dataset.productId
    this.bundleProductsValue = this.bundleProductsValue.filter(
      p => p.id.toString() !== productId
    )
    
    console.log(`Removed product ${productId} from bundle`)
    this.updateUI()
  }

  // Clear all products from bundle
  clearBundle() {
    if (this.bundleProductsValue.length === 0) return
    
    if (confirm('Are you sure you want to clear all products from the bundle?')) {
      this.bundleProductsValue = []
      this.updateUI()
    }
  }

  // Update UI based on current state
  updateUI() {
    this.updateProductCount()
    this.updateSelectedProducts()
    this.updateSaveButton()
    this.updateFormData()
  }

  // Update product count displays
  updateProductCount() {
    const count = this.bundleProductsValue.length
    const text = `${count} product${count !== 1 ? 's' : ''}`
    
    if (this.hasProductCountTarget) {
      this.productCountTarget.textContent = text
    }
    
    if (this.hasProductCountBadgeTarget) {
      this.productCountBadgeTarget.textContent = text
    }
  }

  // Update selected products display
  updateSelectedProducts() {
    const hasProducts = this.bundleProductsValue.length > 0
    
    // Show/hide empty state
    if (this.hasEmptyStateTarget) {
      this.emptyStateTarget.style.display = hasProducts ? 'none' : 'block'
    }
    
    // Show/hide selected products
    if (this.hasSelectedProductsTarget) {
      this.selectedProductsTarget.style.display = hasProducts ? 'grid' : 'none'
      
      if (hasProducts) {
        this.selectedProductsTarget.innerHTML = this.renderSelectedProducts()
      }
    }
  }

  // Render selected products
  renderSelectedProducts() {
    return this.bundleProductsValue.map(product => `
      <div class="selected-product-card">
        <button type="button" 
                class="remove-product-btn"
                data-product-id="${product.id}"
                data-action="click->bundle-builder#removeProduct">
          ×
        </button>
        <div class="product-image">
          ${product.image_url ? 
            `<img src="${product.image_url}" alt="${product.title}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">` :
            `<span style="font-size: 1.5rem;">📦</span>`
          }
        </div>
        <div class="product-name">${product.title}</div>
        <div class="product-price">$${parseFloat(product.price || 0).toFixed(2)}</div>
      </div>
    `).join('')
  }

  // Update save button state
  updateSaveButton() {
    if (this.hasSaveButtonTarget) {
      this.saveButtonTarget.disabled = this.bundleProductsValue.length === 0
    }
  }

  // Update form data for submission
  updateFormData() {
    if (this.hasBundleDataInputTarget) {
      const bundleData = {
        products: this.bundleProductsValue.map(product => ({
          id: product.id,
          title: product.title,
          price: product.price,
          sku: product.sku
        }))
      }
      
      this.bundleDataInputTarget.value = JSON.stringify(bundleData)
    }
  }
}
