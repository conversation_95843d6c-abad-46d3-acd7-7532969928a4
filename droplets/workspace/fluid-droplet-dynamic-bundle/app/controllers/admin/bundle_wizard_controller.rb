# frozen_string_literal: true

module Admin
  # Controller for the Bundle Creation Wizard
  # Handles the step-by-step process of creating a new dynamic bundle
  class BundleWizardController < ApplicationController
    layout 'bundle_admin'

    before_action :authenticate_admin!
    before_action :ensure_company_context
    before_action :validate_step, only: [:step, :process_step]
    before_action :load_wizard, except: [:start]
    before_action :debug_request

    # Temporarily skip CSRF for testing
    skip_before_action :verify_authenticity_token, only: [:complete]

    WIZARD_STEPS = %w[info builder preview].freeze

    # GET /admin/bundles/wizard
    # Starts the wizard with Step 1: Bundle Info
    def start
      Rails.logger.info("WIZARD STATE MACHINE: START action called")

      # Clear any existing wizard and create new one
      session.delete(:bundle_wizard)

      @wizard = BundleWizard.new
      @wizard.save_to_session(session)

      Rails.logger.info("WIZARD STATE MACHINE: Started new wizard in state: #{@wizard.aasm.current_state}")
      Rails.logger.info("WIZARD STATE MACHINE: Session data: #{session[:bundle_wizard]}")

      # Start with step 1 (bundle info)
      Rails.logger.info("WIZARD STATE MACHINE: Redirecting to info step")
      redirect_to bundle_wizard_step_admin_bundles_path(step: 'info')
    end

    # GET /admin/bundles/wizard/step/:step
    # Shows a specific wizard step
    def step
      Rails.logger.info("WIZARD STATE MACHINE: STEP action called with step: #{@current_step}")

      # TEMPORARILY DISABLED: Check if wizard can proceed to this step
      # can_proceed = @wizard.can_proceed_to_step?(@current_step)
      # Rails.logger.info("WIZARD STATE MACHINE: Can proceed to step #{@current_step}? #{can_proceed}")

      # unless can_proceed
      #   Rails.logger.warn("WIZARD STATE MACHINE: Cannot proceed to step #{@current_step} from state #{@wizard.aasm.current_state}")
      #   Rails.logger.warn("WIZARD STATE MACHINE: Wizard current_step: #{@wizard.current_step}")
      #   flash[:error] = "Please complete previous steps first."
      #   redirect_to bundle_wizard_step_admin_bundles_path(step: @wizard.current_step)
      #   return
      # end

      @step_number = @wizard.step_number
      @total_steps = WIZARD_STEPS.length
      @wizard_steps = WIZARD_STEPS

      Rails.logger.info("🔍 STEP METHOD (GET) called with step: #{@current_step}")
      Rails.logger.info("WIZARD STATE MACHINE: Showing step #{@current_step} (state: #{@wizard.aasm.current_state})")

      case @current_step
      when 'info'
        render_info_step
      when 'builder'
        render_builder_step
      when 'preview'
        Rails.logger.info("🔍 TRYING TO RENDER PREVIEW STEP - PARAMS: #{params.inspect}")
        Rails.logger.info("🔍 CURRENT STEP: #{@current_step}")
        Rails.logger.info("🔍 WIZARD STATE: #{@wizard&.aasm&.current_state}")
        render_preview_step
      else
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'info')
      end
    end

    # POST /admin/bundles/wizard/step/:step
    # Processes form submission for a step
    def process_step
      puts "🚀🚀🚀 PROCESS_STEP METHOD (POST) called with step: #{@current_step} 🚀🚀🚀"
      puts "🚀 REQUEST METHOD: #{request.method}"
      Rails.logger.info("🚀 PROCESS_STEP METHOD (POST) called with step: #{@current_step}")
      case @current_step
      when 'info'
        process_info_step
      when 'builder'
        process_builder_step
      when 'preview'
        redirect_to complete_bundle_wizard_admin_bundles_path
      else
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'info')
      end
    end

    # POST /admin/bundles/wizard/complete
    # Completes the wizard and creates the final bundle in Fluid
    def complete
      Rails.logger.info("🚀 WIZARD COMPLETE: Starting complete action")
      @wizard = BundleWizard.from_session(session)
      Rails.logger.info("🚀 WIZARD COMPLETE: Wizard state: #{@wizard&.aasm&.current_state}")
      Rails.logger.info("🚀 WIZARD COMPLETE: ready_to_save? #{@wizard&.ready_to_save?}")

      unless @wizard&.ready_to_save?
        Rails.logger.warn("🚨 WIZARD COMPLETE: Not ready to save, redirecting to info")
        flash[:error] = "Bundle is not ready to save. Please complete all steps."
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'info')
        return
      end

      Rails.logger.info("🚀 WIZARD COMPLETE: Creating bundle in Fluid: #{@wizard.name}")

      # Create the final bundle with all configuration in Fluid
      result = Fluid::BundlesService.call(
        action: :create_shell,
        name: @wizard.name,
        sku: @wizard.sku,
        description: @wizard.description,
        metadata: {
          bundle_type: 'dynamic_bundle',
          categories: @wizard.categories || [],
          products: @wizard.products || [],
          created_via: 'wizard',
          wizard_version: '2.0'
        },
        company: @company
      )

      if result.success?
        # Mark wizard as completed and clear from session
        @wizard.complete_wizard
        @wizard.clear_from_session(session)

        bundle_data = result.data[:bundle]
        flash[:success] = "Bundle '#{@wizard.name}' created successfully!"

        Rails.logger.info("WIZARD Complete: Bundle created successfully with ID: #{bundle_data['id']}")
        redirect_to admin_bundles_path
      else
        Rails.logger.error("WIZARD Complete: Failed to create bundle: #{result.error}")
        flash[:error] = "Failed to create bundle: #{result.error}"
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'preview')
      end
    rescue => e
      Rails.logger.error("WIZARD Complete: Exception creating bundle: #{e.message}")
      flash[:error] = "An unexpected error occurred: #{e.message}"
      redirect_to bundle_wizard_step_admin_bundles_path(step: 'preview')
    end

    private

    def load_wizard
      @wizard = BundleWizard.from_session(session)

      # If no wizard in session and we're not in start action, redirect to start
      unless @wizard.present? && session[:bundle_wizard].present?
        Rails.logger.warn("WIZARD STATE MACHINE: No wizard found in session")

        # Only redirect if we're not already in the start action to avoid loops
        unless action_name == 'start'
          Rails.logger.warn("WIZARD STATE MACHINE: Redirecting to start")
          redirect_to start_bundle_wizard_admin_bundles_path
          return
        end
      end

      if @wizard.present?
        Rails.logger.info("WIZARD STATE MACHINE: Loaded wizard in state: #{@wizard.aasm.current_state}")
      end
    end

    def validate_step
      @current_step = params[:step]
      Rails.logger.info("WIZARD STATE MACHINE: VALIDATE_STEP called with step: #{@current_step}")

      unless WIZARD_STEPS.include?(@current_step)
        Rails.logger.warn("WIZARD STATE MACHINE: Invalid step #{@current_step}, redirecting to info")
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'info')
        return
      end

      Rails.logger.info("WIZARD STATE MACHINE: Step #{@current_step} is valid")
    end

    # Step 1: Bundle Information
    def render_info_step
      set_contextual_header("📦 Create Bundle - Step 1: Bundle Information")
    end

    def process_info_step
      Rails.logger.info("WIZARD INFO: Processing info step with params: #{bundle_params.inspect}")

      if @wizard.update_info(bundle_params)
        Rails.logger.info("WIZARD INFO: After update - Name: #{@wizard.name}, SKU: #{@wizard.sku}")
        @wizard.save_to_session(session)

        Rails.logger.info("WIZARD STATE MACHINE: Info completed, state: #{@wizard.aasm.current_state}")

        # Go to step 2: Builder
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'builder')
      else
        Rails.logger.warn("WIZARD STATE MACHINE: Info validation failed: #{@wizard.errors.full_messages}")
        set_contextual_header("📦 Create Bundle - Step 1: Bundle Information")
        render :step
      end
    end

    # Step 2: Bundle Builder (Drag & Drop)
    def render_builder_step
      Rails.logger.info("🔥 RENDER_BUILDER_STEP CALLED - THIS SHOULD NOT HAPPEN FOR PREVIEW!")
      Rails.logger.info("🔥 REQUEST PATH: #{request.path}")
      Rails.logger.info("🔥 PARAMS: #{params.inspect}")
      Rails.logger.info("🔥 @current_step: #{@current_step}")

      # The wizard is already loaded and validated in before_action
      # Load real categories and products for the builder

      @categories = fetch_categories
      set_contextual_header("🏗️ Create Bundle - Step 2: Configure Products")

      Rails.logger.info("WIZARD STATE MACHINE: Rendering builder step with wizard: #{@wizard.name}")
      Rails.logger.info("WIZARD STATE MACHINE: Loaded #{@categories.size} categories")
    end

    def process_builder_step
      # Debug: Log all params
      Rails.logger.info("WIZARD BUILDER: All params: #{params.inspect}")
      Rails.logger.info("WIZARD BUILDER: builder_config param: #{params[:builder_config].inspect}")

      # Process builder configuration
      builder_config_raw = params[:builder_config]
      Rails.logger.info("WIZARD BUILDER: Raw config before parse: '#{builder_config_raw}'")

      # Handle empty string or nil
      if builder_config_raw.blank?
        builder_config_raw = '{}'
        Rails.logger.info("WIZARD BUILDER: Using default empty config")
      end

      builder_config = JSON.parse(builder_config_raw).with_indifferent_access
      Rails.logger.info("WIZARD BUILDER: Processing builder config: #{builder_config.inspect}")

      # Update wizard with builder configuration
      @wizard.categories = builder_config[:categories] || []
      @wizard.products = builder_config[:products] || []

      if @wizard.complete_builder
        @wizard.save_to_session(session)

        Rails.logger.info("WIZARD STATE MACHINE: Builder completed, state: #{@wizard.aasm.current_state}")

        respond_to do |format|
          format.html { redirect_to bundle_wizard_step_admin_bundles_path(step: 'preview') }
          format.json { render json: { success: true, redirect_url: bundle_wizard_step_admin_bundles_path(step: 'preview') } }
        end
      else
        Rails.logger.warn("WIZARD STATE MACHINE: Builder validation failed")

        respond_to do |format|
          format.html do
            flash[:error] = "Please configure at least one product or category."
            redirect_to bundle_wizard_step_admin_bundles_path(step: 'builder')
          end
          format.json { render json: { success: false, error: "Please configure at least one product or category." } }
        end
      end
    end

    # Step 3: Preview & Save
    def render_preview_step
      Rails.logger.info("🔍 RENDER_PREVIEW_STEP: Wizard state: #{@wizard.aasm.current_state}")
      Rails.logger.info("🔍 RENDER_PREVIEW_STEP: ready_to_save? #{@wizard.ready_to_save?}")
      Rails.logger.info("🔍 RENDER_PREVIEW_STEP: builder_complete? #{@wizard.builder_complete?}")

      # Ensure wizard is ready for preview/save
      unless @wizard.ready_to_save? || @wizard.builder_complete? || @wizard.info_complete?
        flash[:error] = "Please complete previous steps first."
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'info')
        return
      end

      # If wizard is builder_complete, transition to ready_to_save
      if @wizard.builder_complete? && @wizard.may_ready_for_preview?
        Rails.logger.info("🔍 RENDER_PREVIEW_STEP: Transitioning to ready_to_save state")
        @wizard.ready_for_preview
        @wizard.save_to_session(session)
        Rails.logger.info("🔍 RENDER_PREVIEW_STEP: New state: #{@wizard.aasm.current_state}")
      end

      set_contextual_header("👀 Create Bundle - Step 3: Review & Save")
    end

    # Helper methods
    def set_contextual_header(title)
      @page_title = title
      @contextual_header = true
    end



    def fetch_categories
      begin
        Rails.logger.info("BUILDER: Starting to fetch categories...")

        # Check if settings exist and show credentials
        begin
          setting = Setting.fluid_api
          Rails.logger.info("BUILDER: fluid_api setting found:")
          Rails.logger.info("BUILDER: - base_url: #{setting.values['base_url']}")
          Rails.logger.info("BUILDER: - api_key: #{setting.values['api_key'] ? setting.values['api_key'][0..10] + '...' : 'nil'}")
        rescue => setting_error
          Rails.logger.error("BUILDER: fluid_api setting not found: #{setting_error.message}")
          Rails.logger.info("BUILDER: Available settings: #{Setting.pluck(:name).inspect}")
        end

        client = FluidClient.new
        Rails.logger.info("BUILDER: FluidClient initialized successfully")

        response = client.list_categories(per_page: 50, status: 'all')
        Rails.logger.info("BUILDER: Raw response: #{response.inspect}")

        # Extract categories from response
        real_categories = response.is_a?(Hash) ? response["categories"] || response : response
        real_categories = [] unless real_categories.is_a?(Array)

        Rails.logger.info("BUILDER: Successfully fetched #{real_categories.size} real categories")
        Rails.logger.info("BUILDER: First real category structure: #{real_categories.first.inspect}") if real_categories.any?

        # Normalize real categories to ensure they have required fields and add test products
        real_categories = real_categories.map.with_index do |category, index|
          {
            'id' => category['id'] || category['slug'] || "category_#{SecureRandom.hex(4)}",
            'name' => category['name'] || category['title'] || category['slug']&.humanize || 'Unnamed Category',
            'products' => category['products'] || generate_test_products(3 + index, category['name'] || 'general')
          }
        end

        # Add hardcoded test categories for layout testing
        test_categories = [
          {
            'id' => 'vitamins',
            'name' => 'Vitamins & Minerals',
            'products' => generate_test_products(2)
          },
          {
            'id' => 'proteins',
            'name' => 'Protein Powders',
            'products' => generate_test_products(3)
          },
          {
            'id' => 'pre_workout',
            'name' => 'Pre-Workout',
            'products' => generate_test_products(4)
          },
          {
            'id' => 'post_workout',
            'name' => 'Post-Workout Recovery',
            'products' => generate_test_products(5)
          },
          {
            'id' => 'weight_management',
            'name' => 'Weight Management',
            'products' => generate_test_products(6)
          },
          {
            'id' => 'digestive_health',
            'name' => 'Digestive Health',
            'products' => generate_test_products(12)
          },
          {
            'id' => 'immune_support',
            'name' => 'Immune Support',
            'products' => generate_test_products(20)
          },
          {
            'id' => 'energy_focus',
            'name' => 'Energy & Focus',
            'products' => generate_test_products(8)
          },
          {
            'id' => 'joint_support',
            'name' => 'Joint & Bone Support',
            'products' => generate_test_products(7)
          },
          {
            'id' => 'heart_health',
            'name' => 'Heart Health',
            'products' => generate_test_products(9)
          }
        ]

        # Combine real and test categories
        all_categories = real_categories + test_categories

        Rails.logger.info("BUILDER: Total categories (real + test): #{all_categories.size}")
        all_categories
      rescue => e
        Rails.logger.error("BUILDER: Failed to fetch categories: #{e.message}")
        Rails.logger.error("BUILDER: Error backtrace: #{e.backtrace.first(5).join('\n')}")

        # Return just test categories if API fails
        [
          {
            'id' => 'vitamins',
            'name' => 'Vitamins & Minerals',
            'products' => generate_test_products(2)
          },
          {
            'id' => 'proteins',
            'name' => 'Protein Powders',
            'products' => generate_test_products(3)
          }
        ]
      end
    end

    def generate_test_products(count, category_prefix = 'general')
      products_data = [
        { name: 'Vitamin D3 5000 IU', emoji: '☀️', image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=150&h=150&fit=crop&crop=center' },
        { name: 'Omega-3 Fish Oil', emoji: '🐟', image_url: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Magnesium Glycinate', emoji: '⚡', image_url: 'https://images.unsplash.com/photo-**********-edd951aa8f72?w=150&h=150&fit=crop&crop=center' },
        { name: 'Probiotics 50B CFU', emoji: '🦠', image_url: 'https://images.unsplash.com/photo-1607619056574-7b8d3ee536b2?w=150&h=150&fit=crop&crop=center' },
        { name: 'Whey Protein Vanilla', emoji: '💪', image_url: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=150&h=150&fit=crop&crop=center' },
        { name: 'Casein Protein Chocolate', emoji: '🍫', image_url: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=150&h=150&fit=crop&crop=center' },
        { name: 'Plant Protein Berry', emoji: '🫐', image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Pre-Workout Energy', emoji: '🔥', image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop&crop=center' },
        { name: 'BCAA Complex', emoji: '💊', image_url: 'https://images.unsplash.com/photo-1607619056574-7b8d3ee536b2?w=150&h=150&fit=crop&crop=center' },
        { name: 'Creatine Monohydrate', emoji: '⚡', image_url: 'https://images.unsplash.com/photo-**********-edd951aa8f72?w=150&h=150&fit=crop&crop=center' },
        { name: 'Beta-Alanine', emoji: '🏃', image_url: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Post-Workout Recovery', emoji: '🧊', image_url: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=150&h=150&fit=crop&crop=center' },
        { name: 'Glutamine Powder', emoji: '💪', image_url: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=150&h=150&fit=crop&crop=center' },
        { name: 'ZMA Complex', emoji: '😴', image_url: 'https://images.unsplash.com/photo-1607619056574-7b8d3ee536b2?w=150&h=150&fit=crop&crop=center' },
        { name: 'Fat Burner Capsules', emoji: '🔥', image_url: 'https://images.unsplash.com/photo-**********-edd951aa8f72?w=150&h=150&fit=crop&crop=center' },
        { name: 'CLA Softgels', emoji: '💊', image_url: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Green Tea Extract', emoji: '🍃', image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=150&h=150&fit=crop&crop=center' },
        { name: 'Garcinia Cambogia', emoji: '🥭', image_url: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=150&h=150&fit=crop&crop=center' },
        { name: 'Digestive Enzymes', emoji: '🌿', image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=150&h=150&fit=crop&crop=center' },
        { name: 'Fiber Supplement', emoji: '🌾', image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Gut Health Complex', emoji: '🦠', image_url: 'https://images.unsplash.com/photo-1607619056574-7b8d3ee536b2?w=150&h=150&fit=crop&crop=center' },
        { name: 'Vitamin C 1000mg', emoji: '🍊', image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=150&h=150&fit=crop&crop=center' },
        { name: 'Zinc Picolinate', emoji: '⚡', image_url: 'https://images.unsplash.com/photo-**********-edd951aa8f72?w=150&h=150&fit=crop&crop=center' },
        { name: 'Elderberry Extract', emoji: '🫐', image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Echinacea', emoji: '🌸', image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=150&h=150&fit=crop&crop=center' },
        { name: 'Caffeine Pills', emoji: '☕', image_url: 'https://images.unsplash.com/photo-1607619056574-7b8d3ee536b2?w=150&h=150&fit=crop&crop=center' },
        { name: 'Nootropic Blend', emoji: '🧠', image_url: 'https://images.unsplash.com/photo-**********-edd951aa8f72?w=150&h=150&fit=crop&crop=center' },
        { name: 'Lion\'s Mane Mushroom', emoji: '🍄', image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=150&h=150&fit=crop&crop=center' },
        { name: 'Rhodiola Rosea', emoji: '🌿', image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Glucosamine Chondroitin', emoji: '🦴', image_url: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Turmeric Curcumin', emoji: '🟡', image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=150&h=150&fit=crop&crop=center' },
        { name: 'MSM Powder', emoji: '💊', image_url: 'https://images.unsplash.com/photo-1607619056574-7b8d3ee536b2?w=150&h=150&fit=crop&crop=center' },
        { name: 'Collagen Peptides', emoji: '✨', image_url: 'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=150&h=150&fit=crop&crop=center' },
        { name: 'CoQ10 Ubiquinol', emoji: '❤️', image_url: 'https://images.unsplash.com/photo-**********-edd951aa8f72?w=150&h=150&fit=crop&crop=center' },
        { name: 'Red Yeast Rice', emoji: '🍚', image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop&crop=center' },
        { name: 'Garlic Extract', emoji: '🧄', image_url: 'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=150&h=150&fit=crop&crop=center' },
        { name: 'Hawthorn Berry', emoji: '🫐', image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=150&h=150&fit=crop&crop=center' }
      ]

      prices = ['$19.99', '$24.99', '$29.99', '$34.99', '$39.99', '$44.99', '$49.99']

      # Use category prefix to create unique product IDs and vary the starting point
      category_hash = category_prefix.hash.abs
      start_index = category_hash % products_data.length

      (1..count).map do |i|
        product_index = (start_index + i - 1) % products_data.length
        product_data = products_data[product_index]
        price = prices[(i - 1) % prices.length]

        {
          'id' => "#{category_prefix}_product_#{i}",
          'name' => product_data[:name],
          'price' => price,
          'emoji' => product_data[:emoji],
          'image_url' => product_data[:image_url],
          'description' => "#{category_prefix.humanize} product #{i} for layout testing"
        }
      end
    end

    # AJAX endpoint to load products by category
    def load_products
      category_id = params[:category_id]

      begin
        client = FluidClient.new
        products = client.get_products_by_category(category_id, per_page: 50)

        Rails.logger.info("BUILDER AJAX: Loaded #{products.size} products for category #{category_id}")

        render json: {
          success: true,
          products: products
        }
      rescue => e
        Rails.logger.error("BUILDER AJAX: Failed to load products: #{e.message}")
        render json: {
          success: false,
          error: e.message
        }
      end
    end

    def bundle_params
      # Try both possible parameter keys
      if params[:bundle_wizard].present?
        params.require(:bundle_wizard).permit(:name, :sku, :description)
      elsif params[:wizard].present?
        params.require(:wizard).permit(:name, :sku, :description)
      else
        Rails.logger.error("WIZARD PARAMS: No valid params found. Available keys: #{params.keys}")
        ActionController::Parameters.new
      end
    end

    def render_categories_step
      # Load available categories from Fluid
      @available_categories = [
        { id: 'supplements', name: 'Supplements', icon: '💊', description: 'Vitamins, minerals, and nutritional supplements' },
        { id: 'nutrition', name: 'Nutrition', icon: '🥗', description: 'Meal replacements and nutritional products' },
        { id: 'fitness', name: 'Fitness', icon: '💪', description: 'Pre-workout, post-workout, and fitness supplements' },
        { id: 'wellness', name: 'Wellness', icon: '🧘', description: 'General wellness and lifestyle products' }
      ]

      @selected_categories = @bundle_draft['selected_categories'] || []
    end

    def render_products_step
      @selected_categories = @bundle_draft['selected_categories'] || []
      
      if @selected_categories.empty?
        flash[:warning] = "Please select at least one category first."
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'categories')
        return
      end

      # Load products for selected categories
      @category_products = load_products_for_categories(@selected_categories)
      @selected_products = @bundle_draft['selected_products'] || {}
    end

    def render_review_step
      @selected_categories = @bundle_draft['selected_categories'] || []
      @selected_products = @bundle_draft['selected_products'] || {}

      # Load available categories for display
      @available_categories = [
        { id: 'supplements', name: 'Supplements', icon: '💊', description: 'Vitamins, minerals, and nutritional supplements' },
        { id: 'nutrition', name: 'Nutrition', icon: '🥗', description: 'Meal replacements and nutritional products' },
        { id: 'fitness', name: 'Fitness', icon: '💪', description: 'Pre-workout, post-workout, and fitness supplements' },
        { id: 'wellness', name: 'Wellness', icon: '🧘', description: 'General wellness and lifestyle products' }
      ]

      # Load products for selected categories
      @category_products = load_products_for_categories(@selected_categories)

      if @selected_categories.empty?
        flash[:warning] = "Please complete the previous steps first."
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'categories')
        return
      end
    end

    def process_categories_step
      selected_categories = params[:categories] || []
      
      if selected_categories.empty?
        flash[:error] = "Please select at least one category."
        redirect_to bundle_wizard_step_admin_bundles_path(step: 'categories')
        return
      end

      # Update bundle draft
      @bundle_draft['selected_categories'] = selected_categories
      session[:bundle_draft] = @bundle_draft

      flash[:success] = "Categories selected! Now choose products for each category."
      redirect_to bundle_wizard_step_admin_bundles_path(step: 'products')
    end

    def process_products_step
      selected_products = params[:products] || {}
      
      # Validate that at least one product is selected for each category
      @bundle_draft['selected_categories'].each do |category_id|
        category_products = selected_products[category_id] || []
        if category_products.empty?
          flash[:error] = "Please select at least one product for each category."
          redirect_to bundle_wizard_step_admin_bundles_path(step: 'products')
          return
        end
      end

      # Update bundle draft
      @bundle_draft['selected_products'] = selected_products
      session[:bundle_draft] = @bundle_draft

      flash[:success] = "Products selected! Please review your bundle configuration."
      redirect_to bundle_wizard_step_admin_bundles_path(step: 'review')
    end

    def load_products_for_categories(category_ids)
      # Mock products for each category
      products = {
        'supplements' => [
          { id: 'vit-d3', name: 'Vitamin D3', price: '$24.99', description: '5000 IU daily supplement' },
          { id: 'omega-3', name: 'Omega-3', price: '$32.99', description: 'Fish oil supplement' },
          { id: 'probiotics', name: 'Probiotics', price: '$28.99', description: '50 billion CFU blend' },
          { id: 'magnesium', name: 'Magnesium', price: '$19.99', description: 'Chelated magnesium' }
        ],
        'nutrition' => [
          { id: 'protein-vanilla', name: 'Protein Powder - Vanilla', price: '$45.99', description: 'Whey protein isolate' },
          { id: 'protein-chocolate', name: 'Protein Powder - Chocolate', price: '$45.99', description: 'Whey protein isolate' },
          { id: 'meal-replacement', name: 'Meal Replacement Shake', price: '$39.99', description: 'Complete nutrition' }
        ],
        'fitness' => [
          { id: 'pre-workout', name: 'Pre-Workout Energy', price: '$34.99', description: 'Caffeine and amino acids' },
          { id: 'bcaa', name: 'BCAA Complex', price: '$29.99', description: 'Branch chain amino acids' },
          { id: 'creatine', name: 'Creatine Monohydrate', price: '$24.99', description: 'Pure creatine powder' }
        ],
        'wellness' => [
          { id: 'multivitamin', name: 'Daily Multivitamin', price: '$22.99', description: 'Complete vitamin blend' },
          { id: 'stress-support', name: 'Stress Support', price: '$26.99', description: 'Adaptogenic herbs' },
          { id: 'sleep-aid', name: 'Sleep Support', price: '$24.99', description: 'Natural sleep aid' }
        ]
      }

      category_ids.each_with_object({}) do |category_id, result|
        result[category_id] = products[category_id] || []
      end
    end

    def create_final_bundle(bundle_data)
      Rails.logger.info("Creating final bundle in Fluid: #{bundle_data.inspect}")

      # Prepare bundle payload for Fluid API
      fluid_payload = {
        name: bundle_data['name'],
        sku: bundle_data['sku'],
        description: bundle_data['description'],
        status: 'draft',
        categories: prepare_categories_for_fluid(bundle_data['categories'], bundle_data['products'])
      }

      # Call Fluid API to create bundle
      result = Fluid::BundlesService.call(
        action: :create,
        bundle_data: fluid_payload,
        company: @company
      )

      if result.success?
        bundle_id = result.data[:bundle_id] || result.data['id']
        Rails.logger.info("Bundle created successfully in Fluid with ID: #{bundle_id}")

        { success: true, bundle_id: bundle_id }
      else
        Rails.logger.error("Fluid API error: #{result.error}")
        { success: false, error: result.error }
      end
    rescue => e
      Rails.logger.error("Failed to create bundle: #{e.message}")
      { success: false, error: "Failed to create bundle: #{e.message}" }
    end

    def prepare_categories_for_fluid(selected_categories, selected_products)
      return [] unless selected_categories.is_a?(Array) && selected_products.is_a?(Hash)

      category_names = {
        'supplements' => 'Supplements',
        'nutrition' => 'Nutrition',
        'fitness' => 'Fitness',
        'wellness' => 'Wellness'
      }

      selected_categories.map.with_index do |category_id, index|
        category_products = selected_products[category_id] || []

        {
          name: category_names[category_id] || category_id.humanize,
          position: index + 1,
          required: true, # Default to required
          max_selections: 1, # Default to 1
          products: prepare_products_for_fluid(category_products, category_id)
        }
      end
    end

    def prepare_products_for_fluid(product_ids, category_id)
      return [] unless product_ids.is_a?(Array)

      # Load product data
      all_products = load_products_for_categories([category_id])
      category_products = all_products[category_id] || []

      product_ids.map do |product_id|
        product = category_products.find { |p| p[:id] == product_id }
        next unless product

        {
          name: product[:name],
          price: product[:price].to_s.gsub('$', '').to_f,
          external_id: product[:id],
          description: product[:description] || ''
        }
      end.compact
    end

    # Placeholder methods from parent controller
    def authenticate_admin!
      if Rails.env.development?
        Rails.logger.info("DEVELOPMENT: Skipping authentication")
        return true
      end
      redirect_to root_path unless current_user&.admin?
    end

    def ensure_company_context
      unless @company
        if Rails.env.development?
          @company = OpenStruct.new(
            fluid_company_id: *********,
            name: "Development Company",
            droplet_installation_uuid: "dev-dri-123",
            authentication_token: "your_real_fluid_token_here"
          )
          Rails.logger.info("HARDCODED: Using development company")
          return
        end
        handle_missing_company_context
      end
    end

    def debug_request
      puts "🔍 DEBUG: HTTP METHOD: #{request.method}"
      Rails.logger.info("🔍 DEBUG: #{request.method} #{request.path}")
      Rails.logger.info("🔍 DEBUG: Action: #{action_name}")
      Rails.logger.info("🔍 DEBUG: Params: #{params.inspect}")
      Rails.logger.info("🔍 DEBUG: Step param: #{params[:step]}")
    end
  end
end
