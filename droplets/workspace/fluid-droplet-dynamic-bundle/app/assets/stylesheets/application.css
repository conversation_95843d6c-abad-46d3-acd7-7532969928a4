/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

/* Bundle Wizard Styles */
.category-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
}

.category-card:hover {
  background: #e9ecef;
  border-color: #007bff;
  transform: translateY(-1px);
}

.category-card.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.category-card.active .text-muted {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Basic application styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Bundle wizard specific styles */
.wizard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.bundle-builder-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.product-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px;
  transition: all 0.2s ease;
  cursor: grab;
  user-select: none;
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 70px;
}

.product-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.product-thumbnail {
  width: 50px;
  height: 50px;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px;
}

.product-card .product-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-spacer {
  flex: 1;
}

.product-info {
  width: 50px;
  height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: right;
  flex-shrink: 0;
}

.product-name {
  font-size: 12px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 11px;
  font-weight: 700;
  color: #059669;
}



/* Product Item Card - Similar to category card but for products */
.product-item-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
  cursor: grab;
  user-select: none;
  margin-bottom: 8px;
}

.product-item-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.product-item-card .product-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.product-item-card .product-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.product-item-card .product-price {
  font-size: 14px;
  font-weight: 600;
  color: #28a745;
  flex-shrink: 0;
}

.product-card:hover {
  background: #e9ecef;
  border-color: #007bff;
  transform: translateY(-1px);
}

.product-image {
  width: 30px;
  height: 30px;
  margin: 0 auto 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.product-name {
  font-size: 0.7rem;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 0.65rem;
  color: #28a745;
  font-weight: 600;
}

.product-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.bundle-drop-zone-full {
  flex: 1;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 8px;
  margin: 8px;
  background: #f8f9fa;
  overflow-y: auto;
  position: relative;
}

.bundle-drop-zone-full.drag-over {
  border-color: #28a745;
  background-color: #f8fff8;
}

.drop-zone-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 120px;
  text-align: center;
}

.drop-zone-icon {
  font-size: 48px;
  opacity: 0.5;
}

.bundle-product-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
}

.bundle-product-info {
  flex: 1;
}

.bundle-product-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
}

.bundle-product-price {
  font-size: 12px;
  color: #6c757d;
}
