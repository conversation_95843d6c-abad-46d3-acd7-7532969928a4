/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

/* Basic application styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}



/* Product Item Card - Similar to category card but for products */
.product-item-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
  cursor: grab;
  user-select: none;
  margin-bottom: 8px;
}

.product-item-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.product-item-card .product-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.product-item-card .product-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.product-item-card .product-price {
  font-size: 14px;
  font-weight: 600;
  color: #28a745;
  flex-shrink: 0;
}

.product-card:hover {
  background: #e9ecef;
  border-color: #007bff;
  transform: translateY(-1px);
}

.product-image {
  width: 30px;
  height: 30px;
  margin: 0 auto 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.product-name {
  font-size: 0.7rem;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 0.65rem;
  color: #28a745;
  font-weight: 600;
}

.product-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.bundle-drop-zone-full {
  flex: 1;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 8px;
  margin: 8px;
  background: #f8f9fa;
  overflow-y: auto;
  position: relative;
}

.bundle-drop-zone-full.drag-over {
  border-color: #28a745;
  background-color: #f8fff8;
}

.drop-zone-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 120px;
  text-align: center;
}

.drop-zone-icon {
  font-size: 48px;
  opacity: 0.5;
}

.bundle-product-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
}

.bundle-product-info {
  flex: 1;
}

.bundle-product-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
}

.bundle-product-price {
  font-size: 12px;
  color: #6c757d;
}
