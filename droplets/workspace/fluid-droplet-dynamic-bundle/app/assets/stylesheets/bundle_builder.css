/* Bundle Builder - Modern CSS */

/* Reset and Base Styles */
.bundle-builder-container {
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Styles */
.builder-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.builder-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
}

.builder-title i {
  color: #3b82f6;
}

/* Main Layout */
.builder-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Panel Styles */
.builder-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: fit-content;
}

.panel-header {
  background: #f1f5f9;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #334155;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.panel-content {
  padding: 1.5rem;
}

/* Category Cards */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.category-card {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.category-card:hover {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.category-card.active {
  border-color: #3b82f6;
  background: #dbeafe;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.category-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.category-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #475569;
  margin: 0;
}

.category-count {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 0.25rem;
}

/* Product Cards */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.product-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
}

.product-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.product-card:active {
  cursor: grabbing;
  transform: scale(0.95);
}

.product-image {
  width: 48px;
  height: 48px;
  margin: 0 auto 0.75rem;
  background: #f1f5f9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.product-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.product-price {
  font-size: 0.75rem;
  color: #059669;
  font-weight: 600;
}

/* Bundle Drop Zone */
.bundle-drop-zone {
  min-height: 300px;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  padding: 2rem;
  background: #f8fafc;
  transition: all 0.3s ease;
  position: relative;
}

.bundle-drop-zone.drag-over {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.02);
}

.drop-zone-empty {
  text-align: center;
  color: #64748b;
  padding: 2rem;
}

.drop-zone-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.drop-zone-text {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.drop-zone-hint {
  font-size: 0.875rem;
  opacity: 0.7;
}

/* Selected Products */
.selected-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 1rem;
}

.selected-product-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  position: relative;
  transition: all 0.2s ease;
}

.selected-product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.remove-product-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-product-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* Action Bar */
.builder-actions {
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  display: flex;
  justify-content: between;
  align-items: center;
  position: sticky;
  bottom: 0;
  z-index: 50;
}

.bundle-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.product-count-badge {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #e2e8f0;
  color: #334155;
}

/* Responsive Design */
@media (max-width: 768px) {
  .builder-main {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }
  
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.product-card,
.category-card,
.selected-product-card {
  animation: fadeIn 0.3s ease;
}

/* Scrollbar Styling */
.products-grid::-webkit-scrollbar {
  width: 6px;
}

.products-grid::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.products-grid::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.products-grid::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
