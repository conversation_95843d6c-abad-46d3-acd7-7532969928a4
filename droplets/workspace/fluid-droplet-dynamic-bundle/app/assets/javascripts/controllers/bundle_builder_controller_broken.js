// Bundle Builder Controller
// Handles drag & drop product selection and bundle building
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "categoryGrid", 
    "productGrid", 
    "dropZone", 
    "selectedProducts",
    "emptyState",
    "productCount"
  ]
  static values = { 
    selectedCategory: String,
    bundleProducts: Array
  }

  connect() {
    console.log("🏗️ Bundle Builder Controller connected")
    console.log("Selected category:", this.selectedCategoryValue)
    console.log("Bundle products:", this.bundleProductsValue)
    console.log("🎯 Product grid target:", this.hasProductGridTarget ? "✅ Found" : "❌ Missing")
    console.log("🎯 Drop zone target:", this.hasDropZoneTarget ? "✅ Found" : "❌ Missing")

    this.initializeDragAndDrop()
    this.updateProductCount()
    this.updateEmptyState()

    // Load first category products automatically
    this.loadFirstCategory()
  }

  // Load the first category's products on initialization
  loadFirstCategory() {
    const firstCategory = this.categoryGridTarget.querySelector('.category-card.active')
    if (firstCategory) {
      const categoryId = firstCategory.dataset.categoryId
      const categoryName = firstCategory.dataset.categoryName
      console.log(`🎯 Auto-loading first category: ${categoryName}`)
      this.selectedCategoryValue = categoryId
      this.loadCategoryProducts(categoryId)
    } else {
      console.log("❌ No active category found")
      // Try to load the first category anyway
      const firstCategoryCard = this.categoryGridTarget.querySelector('.category-card')
      if (firstCategoryCard) {
        console.log("🎯 Loading first available category")
        firstCategoryCard.click()
      }
    }
  }

  // Initialize drag and drop functionality
  initializeDragAndDrop() {
    console.log("🎯 Initializing drag and drop")
    
    // Setup product cards as draggable
    this.setupDraggableProducts()
    
    // Setup drop zone
    this.setupDropZone()
  }

  // Setup product cards as draggable
  setupDraggableProducts() {
    const productCards = this.productGridTarget.querySelectorAll('[draggable="true"]')
    console.log(`🎯 Setting up ${productCards.length} draggable products`)

    productCards.forEach(card => {
      card.draggable = true
      
      card.addEventListener('dragstart', (e) => {
        console.log('🎯 Drag started:', card.dataset.productName)
        console.log('🎯 Product data:', {
          id: card.dataset.productId,
          name: card.dataset.productName,
          price: card.dataset.productPrice,
          image: card.dataset.productImage
        })
        e.dataTransfer.setData('text/plain', JSON.stringify({
          id: card.dataset.productId,
          name: card.dataset.productName,
          price: card.dataset.productPrice,
          image: card.dataset.productImage
        }))
        card.classList.add('dragging')
      })
      
      card.addEventListener('dragend', (e) => {
        console.log('🎯 Drag ended')
        card.classList.remove('dragging')
      })
    })
  }

  // Setup drop zone for products
  setupDropZone() {
    if (!this.hasDropZoneTarget) {
      console.log('❌ Drop zone not found')
      return
    }

    const dropZone = this.dropZoneTarget
    
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault()
      dropZone.classList.add('drag-over')
    })
    
    dropZone.addEventListener('dragleave', (e) => {
      if (!dropZone.contains(e.relatedTarget)) {
        dropZone.classList.remove('drag-over')
      }
    })
    
    dropZone.addEventListener('drop', (e) => {
      e.preventDefault()
      dropZone.classList.remove('drag-over')
      
      try {
        const productData = JSON.parse(e.dataTransfer.getData('text/plain'))
        console.log('🎯 Product dropped:', productData.name)
        this.addProductToBundle(productData)
      } catch (error) {
        console.error('❌ Error dropping product:', error)
      }
    })
  }

  // Handle category selection
  selectCategory(event) {
    event.preventDefault()
    const categoryButton = event.currentTarget
    const categoryId = categoryButton.dataset.categoryId
    const categoryName = categoryButton.dataset.categoryName

    console.log(`📂 Category selected: ${categoryName} (${categoryId})`)

    // Update selected category
    this.selectedCategoryValue = categoryId

    // Update UI
    this.updateCategorySelection(categoryButton)

    // Load products for this category
    this.loadCategoryProducts(categoryId)
  }

  // Update category selection UI
  updateCategorySelection(selectedButton) {
    console.log('🎯 Updating category selection UI')

    // Remove active class from all category buttons
    this.categoryGridTarget.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('active', 'border-primary')
    })

    // Add active class to selected button
    selectedButton.classList.add('active', 'border-primary')
    console.log('✅ Category selection UI updated')
  }

  // Load products for selected category
  loadCategoryProducts(categoryId) {
    console.log(`📦 Loading products for category: ${categoryId}`)

    // Get products from the category element's data attribute
    const categoryElement = this.categoryGridTarget.querySelector(`[data-category-id="${categoryId}"]`)
    if (!categoryElement) {
      console.error('❌ Category element not found for ID:', categoryId)
      console.log('Available categories:', this.categoryGridTarget.querySelectorAll('[data-category-id]'))
      return
    }

    console.log('✅ Category element found:', categoryElement)
    console.log('Raw products data:', categoryElement.dataset.products)

    let products = []
    try {
      const rawProducts = categoryElement.dataset.products || '[]'
      products = JSON.parse(rawProducts)
      console.log(`📦 Parsed ${products.length} products:`, products)
    } catch (error) {
      console.error('❌ Error parsing products:', error)
      console.log('Raw data that failed to parse:', categoryElement.dataset.products)
      products = []
    }

    this.renderProducts(products)
  }

  // Render products from category data
  renderProducts(products) {
    console.log(`🎯 Rendering ${products ? products.length : 0} products`)
    console.log('🎯 Products data:', products)
    console.log('🎯 productGridTarget exists:', !!this.productGridTarget)

    // Show loading state
    this.productGridTarget.innerHTML = `
      <div class="col-12">
        <div class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading products...</span>
          </div>
          <p class="mt-2 text-muted mb-0">Loading products...</p>
        </div>
      </div>
    `

    // Simulate loading delay for better UX
    setTimeout(() => {
      if (products && products.length > 0) {
        console.log(`🎯 Creating HTML for ${products.length} products`)

        this.productGridTarget.innerHTML = products.map(product => `
          <div draggable="true"
               data-product-id="${product.id}"
               data-product-name="${product.title || product.name}"
               data-product-price="${product.price_in_currency || product.price}"
               data-product-image="${product.image_url || ''}"
               style="background: #ffffff; border: 1px solid #e9ecef; border-radius: 8px; padding: 12px; display: flex; align-items: center; gap: 12px; margin-bottom: 8px; cursor: grab; transition: all 0.2s ease;"
               onmouseover="this.style.borderColor='#007bff'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'"
               onmouseout="this.style.borderColor='#e9ecef'; this.style.boxShadow='none'">
            <div style="font-size: 20px; flex-shrink: 0;">📦</div>
            <div style="flex: 1; font-size: 14px; font-weight: 500; color: #333;">${product.title || product.name}</div>
            <div style="font-size: 14px; font-weight: 600; color: #28a745; flex-shrink: 0;">${product.price_in_currency || product.price}</div>
          </div>
        `).join('')
        console.log(`✅ Products rendered successfully`)
      } else {
        this.productGridTarget.innerHTML = `
          <div class="col-12">
            <div class="text-center py-4 text-muted">
              <div class="mb-2">📦</div>
              <div>No products in this category</div>
            </div>
          </div>
        `
      }

      // Re-initialize drag and drop for new products
      this.setupDraggableProducts()
    }, 100)
  }

  // Add product to bundle
  addProductToBundle(product) {
    console.log('🚀 ADDPRODUCTTOBUNDLE METHOD CALLED WITH:', product.name)
    // Check if product already exists
    if (this.bundleProductsValue.some(p => p.id === product.id)) {
      console.log('⚠️ Product already in bundle')
      return
    }
    
    // Add to bundle products array
    this.bundleProductsValue = [...this.bundleProductsValue, product]
    
    // Update UI
    console.log('🔄 About to call renderSelectedProducts...')
    try {
      this.renderSelectedProducts()
      console.log('✅ renderSelectedProducts completed successfully')
    } catch (error) {
      console.error('❌ Error in renderSelectedProducts:', error)
    }

    this.updateProductCount()
    this.updateEmptyState()

    console.log('✅ Product added to bundle:', product.name)
  }

  // Remove product from bundle
  removeProduct(event) {
    const productId = event.currentTarget.dataset.productId
    console.log(`🗑️ Removing product: ${productId}`)
    
    // Remove from bundle products array
    this.bundleProductsValue = this.bundleProductsValue.filter(p => p.id !== productId)
    
    // Update UI
    this.renderSelectedProducts()
    this.updateProductCount()
    this.updateEmptyState()
  }

  // Render selected products in the bundle
  renderSelectedProducts() {
    console.log('🎨 renderSelectedProducts called')
    console.log('🎯 Has selectedProducts target:', this.hasSelectedProductsTarget)
    console.log('📦 Products to render:', this.bundleProductsValue.length)
    console.log('📦 Products array:', this.bundleProductsValue)

    if (!this.hasSelectedProductsTarget) {
      console.log('❌ No selectedProducts target found!')
      return
    }

    console.log('🎯 selectedProducts target element:', this.selectedProductsTarget)
    console.log('🎯 selectedProducts target innerHTML before:', this.selectedProductsTarget.innerHTML)

    this.selectedProductsTarget.innerHTML = this.bundleProductsValue.map(product => `
      <div class="row g-2 mb-2">
        <div class="col-6">
          <div class="selected-product-item border rounded p-2">
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                <div class="product-image me-2" style="width: 30px; height: 30px; background: #f8f9fa; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem;">
                  ${product.image ? `<img src="${product.image}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">` : '📦'}
                </div>
                <div>
                  <div class="product-name" style="font-size: 0.75rem; font-weight: 600; line-height: 1.2;">${product.name}</div>
                  <small class="text-muted" style="font-size: 0.65rem;">${product.price}</small>
                </div>
              </div>
              <button type="button" class="btn btn-sm btn-outline-danger" style="font-size: 0.7rem; padding: 0.125rem 0.25rem;"
                      data-action="click->bundle-builder#removeProduct"
                      data-product-id="${product.id}">
                ×
              </button>
            </div>
          </div>
        </div>
      </div>
    `).join('')

    console.log('🎯 selectedProducts target innerHTML after:', this.selectedProductsTarget.innerHTML)
    console.log('✅ renderSelectedProducts completed')
  }

  // Update product count display
  updateProductCount() {
    if (this.hasProductCountTarget) {
      this.productCountTarget.textContent = this.bundleProductsValue.length
    }
  }

  // Update empty state visibility
  updateEmptyState() {
    if (this.hasEmptyStateTarget) {
      this.emptyStateTarget.style.display = this.bundleProductsValue.length === 0 ? 'block' : 'none'
    }
  }

  // Mock products data (replace with real API)
  getMockProducts(categoryId) {
    const products = {
      'supplements': [
        { id: '1', name: 'Vitamin D3', price: '$24.99', image: '/images/vitamin-d3.jpg' },
        { id: '2', name: 'Omega-3', price: '$34.99', image: '/images/omega-3.jpg' },
        { id: '3', name: 'Multivitamin', price: '$29.99', image: '/images/multivitamin.jpg' },
        { id: '4', name: 'Probiotics', price: '$39.99', image: '/images/probiotics.jpg' }
      ],
      'skincare': [
        { id: '5', name: 'Face Serum', price: '$49.99', image: '/images/face-serum.jpg' },
        { id: '6', name: 'Moisturizer', price: '$34.99', image: '/images/moisturizer.jpg' },
        { id: '7', name: 'Cleanser', price: '$24.99', image: '/images/cleanser.jpg' },
        { id: '8', name: 'Sunscreen', price: '$29.99', image: '/images/sunscreen.jpg' }
      ]
    }
    
    return products[categoryId] || []
  }
}
