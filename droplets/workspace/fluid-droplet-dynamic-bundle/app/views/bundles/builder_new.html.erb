<%# New Bundle Builder Page - Clean Start %>
<div class="container-fluid py-4">
  <!-- Progress Semaphore -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="progress-semaphore d-flex justify-content-center align-items-center gap-4">
        <div class="progress-step active">
          <div class="step-circle">1</div>
          <div class="step-label">Select Category</div>
        </div>
        <div class="progress-arrow">→</div>
        <div class="progress-step">
          <div class="step-circle">2</div>
          <div class="step-label">Choose Products</div>
        </div>
        <div class="progress-arrow">→</div>
        <div class="progress-step">
          <div class="step-circle">3</div>
          <div class="step-label">Review Bundle</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content - 3 Panels -->
  <div class="row">
    <!-- Panel 1: Categories -->
    <div class="col-md-4">
      <div class="panel-card">
        <div class="panel-header">
          <h5 class="mb-0">📂 Categories</h5>
        </div>
        <div class="panel-body">
          <div class="category-list">
            <% @categories.each do |category| %>
              <div class="category-item <%= 'active' if category[:id] == 'supplements' %>" 
                   data-category-id="<%= category[:id] %>">
                <div class="category-icon"><%= category[:emoji] %></div>
                <div class="category-info">
                  <div class="category-name"><%= category[:name] %></div>
                  <div class="category-count"><%= category[:product_count] %> products</div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Panel 2: Products -->
    <div class="col-md-4">
      <div class="panel-card">
        <div class="panel-header">
          <h5 class="mb-0">📦 Products</h5>
          <small class="text-muted">Supplements</small>
        </div>
        <div class="panel-body">
          <div class="product-list">
            <!-- Products will be loaded here -->
            <div class="loading-state text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2 text-muted">Loading products...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Panel 3: Selected Bundle -->
    <div class="col-md-4">
      <div class="panel-card">
        <div class="panel-header">
          <h5 class="mb-0">🛒 Your Bundle</h5>
          <small class="text-muted">0 products selected</small>
        </div>
        <div class="panel-body">
          <div class="bundle-list">
            <div class="empty-bundle text-center py-4">
              <div class="empty-icon">📦</div>
              <p class="text-muted">No products selected yet</p>
              <small class="text-muted">Drag products here to build your bundle</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="row mt-4">
    <div class="col-12 d-flex justify-content-between">
      <button type="button" class="btn btn-outline-secondary">
        ← Back to Bundles
      </button>
      <button type="button" class="btn btn-primary" disabled>
        Create Bundle →
      </button>
    </div>
  </div>
</div>

<!-- Styles for the new builder -->
<style>
/* Progress Semaphore */
.progress-semaphore {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.progress-step {
  text-align: center;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin: 0 auto 8px;
  transition: all 0.3s ease;
}

.progress-step.active .step-circle {
  background: #007bff;
  color: white;
}

.step-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.progress-step.active .step-label {
  color: #007bff;
  font-weight: 600;
}

.progress-arrow {
  color: #dee2e6;
  font-size: 20px;
  font-weight: bold;
}

/* Panel Cards */
.panel-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.panel-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* Category Items */
.category-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  border: 1px solid transparent;
}

.category-item:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.category-item.active {
  background: #e3f2fd;
  border-color: #007bff;
}

.category-icon {
  font-size: 24px;
  margin-right: 12px;
}

.category-info {
  flex: 1;
}

.category-name {
  font-weight: 500;
  color: #212529;
}

.category-count {
  font-size: 12px;
  color: #6c757d;
}

/* Loading and Empty States */
.loading-state, .empty-bundle {
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.5;
  margin-bottom: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .progress-semaphore {
    flex-direction: column;
    gap: 16px !important;
  }
  
  .progress-arrow {
    transform: rotate(90deg);
  }
  
  .panel-card {
    margin-bottom: 20px;
    height: auto;
  }
}
</style>
