<!-- NEW BUNDLE BUILDER - REBUILT <%= Time.current %> -->
<% content_for :title, "Bundle Builder - #{@bundle['name']}" %>
<%= stylesheet_link_tag 'bundle_builder', 'data-turbo-track': 'reload', 'data-timestamp': Time.current.to_i %>

<div class="bundle-builder-container"
     data-controller="bundle-builder"
     data-bundle-builder-available-products-value="<%= @available_products.to_json.html_safe %>"
     data-bundle-builder-bundle-products-value="[]">

  <!-- Header -->
  <div class="builder-header">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col">
          <h1 class="builder-title">
            <i class="fas fa-cube"></i>
            Bundle Builder
            <small class="text-muted ms-3"><%= @bundle['name'] %></small>
          </h1>
        </div>
        <div class="col-auto">
          <%= link_to admin_bundles_path, class: "btn btn-secondary" do %>
            <i class="fas fa-arrow-left"></i>
            Back to Bundles
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="builder-main">
    <!-- Left Panel - Product Catalog -->
    <div class="builder-panel">
      <div class="panel-header">
        <h3 class="panel-title">
          <i class="fas fa-store"></i>
          Product Catalog
        </h3>
        <div class="panel-actions">
          <input type="search"
                 class="form-control form-control-sm"
                 placeholder="Search products..."
                 data-bundle-builder-target="searchInput"
                 data-action="input->bundle-builder#searchProducts">
        </div>
      </div>

      <div class="panel-content">
        <!-- Categories -->
        <div class="categories-section mb-4">
          <h5 class="mb-3">Categories</h5>
          <div class="categories-grid" data-bundle-builder-target="categoriesGrid">
            <!-- Categories will be loaded here -->
          </div>
        </div>

        <!-- Products -->
        <div class="products-section">
          <h5 class="mb-3">Products</h5>
          <div class="products-grid" data-bundle-builder-target="productsGrid">
            <!-- Products will be loaded here -->
          </div>
        </div>
      </div>
    </div>
    <!-- Right Panel - Bundle Builder -->
    <div class="builder-panel">
      <div class="panel-header">
        <h3 class="panel-title">
          <i class="fas fa-box"></i>
          Bundle Products
        </h3>
        <div class="panel-actions">
          <span class="product-count-badge" data-bundle-builder-target="productCount">
            0 products
          </span>
        </div>
      </div>

      <div class="panel-content">
        <!-- Drop Zone -->
        <div class="bundle-drop-zone"
             data-bundle-builder-target="dropZone"
             data-action="dragover->bundle-builder#handleDragOver
                         dragleave->bundle-builder#handleDragLeave
                         drop->bundle-builder#handleDrop">

          <!-- Empty State -->
          <div class="drop-zone-empty" data-bundle-builder-target="emptyState">
            <div class="drop-zone-icon">📦</div>
            <div class="drop-zone-text">Add products to your bundle</div>
            <div class="drop-zone-hint">Drag products here or click to add</div>
          </div>

          <!-- Selected Products -->
          <div class="selected-products-grid"
               data-bundle-builder-target="selectedProducts"
               style="display: none;">
            <!-- Selected products will appear here -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Bar -->
  <div class="builder-actions">
    <div class="bundle-info">
      <span class="text-muted">Building:</span>
      <strong><%= @bundle['name'] %></strong>
      <span class="product-count-badge" data-bundle-builder-target="productCountBadge">
        0 products
      </span>
    </div>

    <div class="action-buttons">
      <%= form_with url: bundle_builder_admin_bundles_path, method: :post, local: true,
                    data: { bundle_builder_target: "form" } do |form| %>
        <%= form.hidden_field :bundle_data, data: { bundle_builder_target: "bundleDataInput" } %>

        <button type="button"
                class="btn btn-secondary"
                data-action="click->bundle-builder#clearBundle">
          <i class="fas fa-trash"></i>
          Clear All
        </button>

        <button type="submit"
                class="btn btn-primary"
                data-bundle-builder-target="saveButton"
                disabled>
          <i class="fas fa-save"></i>
          Save Bundle
        </button>
      <% end %>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  console.log('Bundle Builder loaded for:', '<%= j @bundle['name'] %>');

  // Initialize any additional functionality here
  const builderElement = document.querySelector('[data-controller="bundle-builder"]');
  if (builderElement) {
    console.log('Bundle Builder controller found');
  }
});
</script>



