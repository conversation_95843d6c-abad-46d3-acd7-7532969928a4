<% content_for :title, "Bundle Builder - #{@bundle['name']}" %>
<%= stylesheet_link_tag 'bundle_builder', 'data-turbo-track': 'reload' %>

<div class="bundle-builder-container"
     data-controller="bundle-builder"
     data-bundle-builder-available-products-value="<%= @available_products.to_json.html_safe %>"
     data-bundle-builder-bundle-products-value="[]">

  <!-- Header -->
  <div class="builder-header">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col">
          <h1 class="builder-title">
            <i class="fas fa-cube"></i>
            Bundle Builder
            <small class="text-muted ms-3"><%= @bundle['name'] %></small>
          </h1>
        </div>
        <div class="col-auto">
          <%= link_to admin_bundles_path, class: "btn btn-secondary" do %>
            <i class="fas fa-arrow-left"></i>
            Back to Bundles
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="builder-main">
    <!-- Left Panel - Product Catalog -->
    <div class="builder-panel">
      <div class="panel-header">
        <h3 class="panel-title">
          <i class="fas fa-store"></i>
          Product Catalog
        </h3>
        <div class="panel-actions">
          <input type="search"
                 class="form-control form-control-sm"
                 placeholder="Search products..."
                 data-bundle-builder-target="searchInput"
                 data-action="input->bundle-builder#searchProducts">
        </div>
      </div>

      <div class="panel-content">
        <!-- Categories -->
        <div class="categories-section mb-4">
          <h5 class="mb-3">Categories</h5>
          <div class="categories-grid" data-bundle-builder-target="categoriesGrid">
            <!-- Categories will be loaded here -->
          </div>
        </div>

        <!-- Products -->
        <div class="products-section">
          <h5 class="mb-3">Products</h5>
          <div class="products-grid" data-bundle-builder-target="productsGrid">
            <!-- Products will be loaded here -->
          </div>
        </div>
      </div>
    </div>
    <!-- Right Panel - Bundle Builder -->
    <div class="builder-panel">
      <div class="panel-header">
        <h3 class="panel-title">
          <i class="fas fa-box"></i>
          Bundle Products
        </h3>
        <div class="panel-actions">
          <span class="product-count-badge" data-bundle-builder-target="productCount">
            0 products
          </span>
        </div>
      </div>

      <div class="panel-content">
        <!-- Drop Zone -->
        <div class="bundle-drop-zone"
             data-bundle-builder-target="dropZone"
             data-action="dragover->bundle-builder#handleDragOver
                         dragleave->bundle-builder#handleDragLeave
                         drop->bundle-builder#handleDrop">

          <!-- Empty State -->
          <div class="drop-zone-empty" data-bundle-builder-target="emptyState">
            <div class="drop-zone-icon">📦</div>
            <div class="drop-zone-text">Add products to your bundle</div>
            <div class="drop-zone-hint">Drag products here or click to add</div>
          </div>

          <!-- Selected Products -->
          <div class="selected-products-grid"
               data-bundle-builder-target="selectedProducts"
               style="display: none;">
            <!-- Selected products will appear here -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Bar -->
  <div class="builder-actions">
    <div class="bundle-info">
      <span class="text-muted">Building:</span>
      <strong><%= @bundle['name'] %></strong>
      <span class="product-count-badge" data-bundle-builder-target="productCountBadge">
        0 products
      </span>
    </div>

    <div class="action-buttons">
      <%= form_with url: bundle_builder_admin_bundles_path, method: :post, local: true,
                    data: { bundle_builder_target: "form" } do |form| %>
        <%= form.hidden_field :bundle_data, data: { bundle_builder_target: "bundleDataInput" } %>

        <button type="button"
                class="btn btn-secondary"
                data-action="click->bundle-builder#clearBundle">
          <i class="fas fa-trash"></i>
          Clear All
        </button>

        <button type="submit"
                class="btn btn-primary"
                data-bundle-builder-target="saveButton"
                disabled>
          <i class="fas fa-save"></i>
          Save Bundle
        </button>
      <% end %>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  console.log('Bundle Builder loaded for:', '<%= j @bundle['name'] %>');

  // Initialize any additional functionality here
  const builderElement = document.querySelector('[data-controller="bundle-builder"]');
  if (builderElement) {
    console.log('Bundle Builder controller found');
  }
});
</script>





  /* Dropped Products Styles */
  .dropped-products {
    margin-top: 10px;
  }

  .dropped-product-item {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .dropped-product-info {
    flex: 1;
  }

  .dropped-product-name {
    font-weight: 600;
    font-size: 13px;
    color: #1976d2;
  }

  .dropped-product-price {
    font-size: 11px;
    color: #666;
  }

  .remove-product-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
  }

  .remove-product-btn:hover {
    background: #f8d7da;
  }

  /* Bundle Drop Zone Styles */
  .bundle-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 20px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    min-height: 300px;
    position: relative;
  }

  .bundle-drop-zone.drag-over {
    border-color: #28a745;
    background: #d4edda;
  }

  .drop-zone-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    height: 260px;
    text-align: center;
  }

  .drop-zone-icon {
    font-size: 48px;
    opacity: 0.5;
  }

  .drop-zone-text {
    font-size: 18px;
    font-weight: 600;
    color: #6c757d;
  }

  /* Copy EXACT same CSS as categories that work */
  .selected-products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 10px;
    max-height: 400px;
    overflow-y: auto;
    scroll-behavior: smooth;
  }

  /* Custom scrollbar styles */
  .store-products-container::-webkit-scrollbar,
  .selected-products-grid::-webkit-scrollbar {
    width: 6px;
  }

  .store-products-container::-webkit-scrollbar-track,
  .selected-products-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .store-products-container::-webkit-scrollbar-thumb,
  .selected-products-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .store-products-container::-webkit-scrollbar-thumb:hover,
  .selected-products-grid::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Drag and Drop States */
  .store-product-card.dragging {
    opacity: 0.5;
    transform: scale(0.95);
    transition: all 0.2s ease;
  }

  .bundle-drop-zone-full.drag-over {
    background-color: #e3f2fd;
    border: 2px dashed #2196f3;
    transform: scale(1.02);
    transition: all 0.2s ease;
  }

  .bundle-drop-zone-full.drag-over .drop-zone-empty {
    color: #2196f3;
  }

  .bundle-drop-zone-full.drag-over .drop-zone-icon {
    animation: bounce 0.6s infinite alternate;
  }

  @keyframes bounce {
    from { transform: translateY(0px); }
    to { transform: translateY(-10px); }
  }

  /* DEBUG: Force grid with red border to see if it works */
  .products-grid {
    border: 2px solid red !important;
    background: rgba(255, 0, 0, 0.1) !important;
  }

  .selected-products-grid {
    border: 2px solid blue !important;
    background: rgba(0, 0, 255, 0.1) !important;
  }

  .selected-product-card {
    background: white;
    border: 1px solid #e3f2fd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    position: relative;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .selected-product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }

  .selected-product-emoji {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .selected-product-name {
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .selected-product-price {
    color: #666;
    font-size: 13px;
  }

  .remove-product-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: opacity 0.2s ease;
  }

  .remove-product-btn:hover {
    opacity: 1;
    transform: scale(1.1);
  }

  /* Selected Products Styles */
  .selected-products-container {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
  }

  .selected-product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    margin-bottom: 8px;
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 6px;
  }

  .selected-product-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .selected-product-emoji {
    font-size: 24px;
  }

  .selected-product-details {
    flex: 1;
  }

  .selected-product-name {
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 2px;
  }

  .selected-product-price {
    font-size: 14px;
    color: #666;
  }

  .product-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .product-info {
    flex: 1;
  }
  
  .product-name {
    font-weight: 500;
    color: #495057;
  }
  
  .product-details {
    font-size: 12px;
    color: #6c757d;
    display: flex;
    gap: 10px;
  }
  
  .product-sku {
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
  }
  
  .drag-handle {
    color: #6c757d;
    cursor: grab;
  }
  
  .bundle-preview {
    padding: 20px;
  }
  
  .preview-header h6 {
    margin-bottom: 4px;
    color: #495057;
  }
  
  .preview-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.4;
  }
  
  .stat {
    display: flex;
    justify-content: between;
    margin-bottom: 5px;
  }
  
  .stat-label {
    font-size: 13px;
    color: #6c757d;
  }
  
  .stat-value {
    font-weight: 500;
    color: #495057;
  }

  /* Drag and Drop Enhancements */
  .product-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }

  /* Hide flash messages and navigation tabs in builder - AGGRESSIVE */
  .alert,
  .alert-success,
  .alert-info,
  .alert-warning,
  div[class*="alert"] {
    display: none !important;
  }

  /* Hide ALL navigation elements during wizard */
  .nav,
  .nav-tabs,
  .nav-pills,
  ul.nav,
  nav,
  [role="navigation"],
  div:has(> a:contains("Bundles")),
  div:has(> a:contains("Categories")),
  div:has(> a:contains("Products")),
  div:has(> a:contains("Settings")) {
    display: none !important;
  }

  /* Target specific Bootstrap tab structure */
  .container .nav,
  .container ul.nav,
  .container .nav-tabs {
    display: none !important;
  }

  /* New Full Screen Layout */
  .bundle-builder-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .builder-header-minimal {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    flex-shrink: 0;
  }

  .builder-main-container {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .builder-left-half {
    width: 50%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e9ecef;
  }

  .builder-right-half {
    width: 50%;
    display: flex;
    flex-direction: column;
  }

  .categories-section {
    height: 50%;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    overflow-y: auto;
    /* Smooth scrolling */
    scroll-behavior: smooth;
  }

  /* Ensure products section has scrolling */
  .categories-section:last-child {
    border-bottom: none;
  }

  .products-section {
    height: 50%;
    padding: 20px;
    overflow-y: auto;
  }

  .section-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  .section-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }

  .bundle-drop-zone-full {
    flex: 1;
    margin: 20px 20px 0 20px;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 20px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    overflow-y: auto;
    position: relative;
  }

  .bundle-drop-zone-full.drag-over {
    border-color: #28a745;
    background: #d4edda;
  }

  .bundle-actions {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    flex-shrink: 0;
  }

  .bundle-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .category-product {
    background: #e8f5e8 !important;
    border-color: #28a745 !important;
    position: relative;
  }

  .category-product .btn-outline-danger {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 2px 6px;
    font-size: 12px;
    line-height: 1;
  }

  .preview-category {
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #667eea;
  }

  .form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  console.log('Bundle Builder loaded for:', '<%= @bundle['name'] %>');

  // Check if this is part of the wizard flow
  const isWizardFlow = window.location.search.includes('bundle_id=') ||
                       document.referrer.includes('/admin/bundles/new') ||
                       sessionStorage.getItem('bundle_wizard_active') === 'true';

  // Set up contextual header for wizard
  if (isWizardFlow && window.updateContextualHeader) {
    const bundleName = '<%= j @bundle['name'] %>';

    window.updateContextualHeader(
      `🧙‍♂️ Bundle Creation Wizard - Step 2/3`,
      [
        {
          text: '← Back to Step 1',
          class: 'btn-outline-secondary',
          onclick: 'backToWizardStep1()'
        },
        {
          text: 'Continue to Review →',
          class: 'btn-primary',
          onclick: 'continueToWizardStep3()'
        }
      ]
    );

    // Mark wizard as active
    sessionStorage.setItem('bundle_wizard_active', 'true');

    // Update save button text and behavior for wizard
    const saveBtnText = document.getElementById('saveBtnText');
    if (saveBtnText) {
      saveBtnText.textContent = 'Continue to Review →';
    }

    // Override save button behavior for wizard
    const saveBtn = document.getElementById('saveBundleBtn');
    if (saveBtn) {
      saveBtn.onclick = continueToWizardStep3;
    }
  }

  // Hide flash messages and navigation tabs immediately in builder
  const alerts = document.querySelectorAll('.alert, .alert-success');
  alerts.forEach(alert => {
    alert.style.display = 'none';
  });

  // Hide navigation tabs
  const navTabs = document.querySelectorAll('.nav-tabs, .nav.nav-tabs, ul.nav, .nav');
  navTabs.forEach(nav => {
    if (nav.textContent.includes('Bundles') || nav.textContent.includes('Categories') || nav.textContent.includes('Products') || nav.textContent.includes('Settings')) {
      nav.style.display = 'none';
    }
  });

  console.log('Bundle Builder initialized with Stimulus');
});

// Global functions for wizard navigation
window.backToWizardStep1 = function() {
  if (confirm('Are you sure you want to go back? Your current progress will be saved.')) {
    // Go back to bundle creation form
    window.location.href = '<%= new_admin_bundle_path %>';
  }
};

window.continueToWizardStep3 = function() {
  // Get the Stimulus controller instance
  const bundleBuilderElement = document.querySelector('[data-controller="bundle-builder"]');
  if (bundleBuilderElement) {
    const controller = bundleBuilderElement.bundleBuilder;
    if (controller && controller.bundleProductsValue.length === 0) {
      alert('Please add some products before continuing.');
      return;
    }
  }

  // Continue to Step 3 (Review)
  window.location.href = '<%= bundle_wizard_step_admin_bundles_path(step: "review") %>';
};

// TEMPORAL: Debug y test
setTimeout(() => {
  console.log('🔍 DEBUGGING: Checking elements...');

  const bundleBuilder = document.querySelector('[data-controller="bundle-builder"]');
  console.log('🔍 Bundle builder element:', bundleBuilder);

  const productGrid = document.querySelector('[data-bundle-builder-target="productGrid"]');
  console.log('🔍 Product grid element:', productGrid);

  const selectedProducts = document.querySelector('[data-bundle-builder-target="selectedProducts"]');
  console.log('🔍 Selected products element:', selectedProducts);

  // Force show some test products
  if (productGrid) {
    console.log('🔍 Forcing test products...');
    console.log('🔍 Product grid classes:', productGrid.className);
    console.log('🔍 Product grid computed style:', window.getComputedStyle(productGrid).display);

    productGrid.innerHTML = `
      <div class="store-product-card" draggable="true">
        <div class="product-emoji">💊</div>
        <div class="product-name">Vitamin D3</div>
        <div class="product-price">$19.99</div>
      </div>
      <div class="store-product-card" draggable="true">
        <div class="product-emoji">🐟</div>
        <div class="product-name">Omega-3</div>
        <div class="product-price">$24.99</div>
      </div>
      <div class="store-product-card" draggable="true">
        <div class="product-emoji">🦠</div>
        <div class="product-name">Probiotics</div>
        <div class="product-price">$29.99</div>
      </div>
      <div class="store-product-card" draggable="true">
        <div class="product-emoji">⚡</div>
        <div class="product-name">Magnesium</div>
        <div class="product-price">$34.99</div>
      </div>
      <div class="store-product-card" draggable="true">
        <div class="product-emoji">🔥</div>
        <div class="product-name">Pre-Workout</div>
        <div class="product-price">$39.99</div>
      </div>
      <div class="store-product-card" draggable="true">
        <div class="product-emoji">💪</div>
        <div class="product-name">Protein Powder</div>
        <div class="product-price">$44.99</div>
      </div>
    `;
    console.log('✅ Test products added');
    console.log('🔍 Product grid after adding products:', window.getComputedStyle(productGrid).display);
  }

  // Force show some selected products
  if (selectedProducts) {
    console.log('🔍 Forcing test selected products...');
    selectedProducts.innerHTML = `
      <div class="selected-product-card">
        <button type="button" class="remove-product-btn">✕</button>
        <div class="selected-product-emoji">💊</div>
        <div class="selected-product-name">Selected Product 1</div>
        <div class="selected-product-price">$19.99</div>
      </div>
      <div class="selected-product-card">
        <button type="button" class="remove-product-btn">✕</button>
        <div class="selected-product-emoji">🐟</div>
        <div class="selected-product-name">Selected Product 2</div>
        <div class="selected-product-price">$24.99</div>
      </div>
    `;
    console.log('✅ Test selected products added');
  }
}, 1000);
</script>
