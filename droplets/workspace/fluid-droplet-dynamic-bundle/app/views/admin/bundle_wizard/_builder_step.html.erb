<!-- Step 2: Bundle Builder -->
<div class="step-content">

  <form id="builder-form"
        action="<%= process_bundle_wizard_step_admin_bundles_path(step: 'builder') %>"
        method="post"
        accept-charset="UTF-8"
        data-turbo-frame="wizard_step"
        data-bundle-wizard-target="form"
        data-controller="bundle-builder"
        data-bundle-builder-selected-category-value=""
        data-bundle-builder-bundle-products-value="[]">
    <%= hidden_field_tag :authenticity_token, form_authenticity_token %>
    <%= hidden_field_tag :_method, 'post' %>
    <%= hidden_field_tag :builder_config, '{}', id: 'builder_config_input' %>





    <!-- Main Builder Interface -->
    <div class="row g-3 mb-4" style="height: 60vh;">
      <!-- Left Column - Store Catalog -->
      <div class="col-6" style="display: flex; flex-direction: column;">

        <!-- Categories Section (Fixed Height) -->
        <div style="flex: 0 0 auto; display: flex; flex-direction: column;">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-light border-0 py-2">
              <h6 class="mb-0">
                <span class="me-2">🏪</span>Store Categories
              </h6>
            </div>
            <div class="card-body p-2" style="overflow-y: auto; max-height: 180px;">
              <div class="row g-2" data-bundle-builder-target="categoryGrid">
                <% if @categories.present? %>
                  <% @categories.each_with_index do |category, index| %>
                    <div class="col-6">
                      <div class="category-card <%= 'active' if index == 0 %>"
                           data-category-id="<%= category['id'] %>"
                           data-category-name="<%= category['name'] %>"
                           data-products='<%= category['products'].to_json %>'
                           data-action="click->bundle-builder#selectCategory"
                           style="cursor: pointer; user-select: none; transition: all 0.2s ease;">
                        <div class="category-icon mb-1">📦</div>
                        <div class="category-name"><%= category['name'] %></div>
                        <small class="text-muted">
                          <%= category['products']&.length || 0 %> items
                        </small>
                      </div>
                    </div>
                  <% end %>
                <% else %>
                  <div class="col-12">
                    <div class="text-center py-3 text-muted">
                      <div class="mb-2">📦</div>
                      <div>No categories found</div>
                      <small>Check your Fluid API connection</small>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <!-- Products Section (Remaining Space) -->
        <div style="flex: 1; display: flex; flex-direction: column; margin-top: 0.75rem;">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-light border-0 py-2">
              <h6 class="mb-0">
                <span class="me-2">📦</span>Products
                <small class="text-muted">Supplements</small>
              </h6>
            </div>
            <div class="card-body p-2" style="overflow-y: auto; max-height: 400px;">
              <div class="row g-1" data-bundle-builder-target="productGrid">
                <!-- Products will be loaded here by Stimulus -->
                <div class="col-12">
                  <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading products...</span>
                    </div>
                    <p class="text-muted mt-2 mb-0">Loading products...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Bundle Drop Zone -->
      <div class="col-6" style="display: flex; flex-direction: column;">

        <div class="card border-0 shadow-sm h-100">
          <div class="card-header bg-light border-0 py-2">
            <h6 class="mb-0">
              <span class="me-2">📦</span>Bundle Products
              <small class="text-muted">(Drop products here)</small>
            </h6>
          </div>
          <div class="card-body p-2" style="overflow-y: auto;">
            <div class="bundle-drop-zone h-100"
                 id="bundleDropZone"
                 data-bundle-builder-target="dropZone">

              <!-- Empty State -->
              <div class="drop-zone-empty text-center d-flex flex-column justify-content-center h-100"
                   id="dropZoneEmpty"
                   data-bundle-builder-target="emptyState">
                <div class="drop-zone-icon mb-3">📦</div>
                <h6 class="text-muted mb-2">Drop products here to add to bundle</h6>
                <p class="text-muted small mb-0">Drag products from the left panel</p>
              </div>

              <!-- TEST CONTROLLER BUTTON -->
              <div data-controller="test">
                <button type="button"
                        data-action="click->test#testMethod"
                        class="btn btn-info btn-sm mb-2">
                  🧪 TEST: Simple Controller
                </button>
              </div>

              <!-- DEBUG BUTTON -->
              <button type="button"
                      data-action="click->bundle-builder#debugAddProduct"
                      class="btn btn-warning btn-sm mb-2">
                🧪 DEBUG: Add Test Product
              </button>

              <!-- Selected Products -->
              <div class="selected-products-container"
                   id="selectedProductsGrid"
                   data-bundle-builder-target="selectedProducts">
                <div class="row g-1">
                  <!-- Selected products will appear here -->
                </div>
              </div>

            </div>
          </div>
        </div>

      </div>
    </div>

  </form>

  <!-- Navigation Footer -->
  <div class="text-center">
    <button type="button"
            class="btn btn-outline-secondary me-3"
            data-bundle-wizard-target="prevButton"
            data-action="click->bundle-wizard#prevStep">
      <span class="me-1">←</span>Cancel
    </button>

    <button type="button"
            class="btn btn-primary"
            data-bundle-wizard-target="nextButton"
            data-action="click->bundle-wizard#nextStep">
      Continue to Preview <span class="ms-1">→</span>
    </button>
  </div>

</div>

<style>
/* Category Cards */
.category-card {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.category-card:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border-color: #adb5bd;
}

.category-card.active {
  background: #e3f2fd;
  border-color: #0d6efd;
  box-shadow: 0 2px 8px rgba(13, 110, 253, 0.2);
}

.category-icon {
  font-size: 1.25rem;
  opacity: 0.8;
}

.category-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.125rem;
  line-height: 1.2;
}

.category-card.active .category-name {
  color: #0d6efd;
}

/* Bundle Drop Zone */
.bundle-drop-zone {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  background: #f8f9fa;
  position: relative;
  transition: all 0.2s ease;
  min-height: 200px;
}

.bundle-drop-zone.drag-over {
  border-color: #28a745;
  background-color: #f8fff8;
  border-style: solid;
}

.drop-zone-icon {
  font-size: 2.5rem;
  opacity: 0.5;
  color: #6c757d;
}

.selected-products-container {
  padding: 0.5rem;
}

.selected-products-container:empty + .drop-zone-empty {
  display: flex;
}

.selected-products-container:not(:empty) + .drop-zone-empty {
  display: none;
}



/* Product Cards */
.product-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.5rem;
  text-align: center;
  cursor: grab;
  transition: all 0.2s ease;
  height: 100px;
  width: 100%;
  max-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.product-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-1px);
  border-color: #adb5bd;
}

.product-card:active {
  cursor: grabbing;
}

.product-card.dragging {
  opacity: 0.5;
  transform: rotate(2deg) scale(0.95);
}

.product-image {
  width: 32px;
  height: 32px;
  background: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.25rem;
  font-size: 1rem;
  object-fit: cover;
}

.product-name {
  font-size: 0.65rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.125rem;
  line-height: 1.1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.product-price {
  color: #28a745;
  font-weight: 600;
  font-size: 0.65rem;
}

/* Bundle Product Cards (in drop zone) */
.bundle-product-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
}

.bundle-product-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.bundle-product-image {
  width: 40px;
  height: 40px;
  background: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.bundle-product-info {
  flex: 1;
  min-width: 0;
}

.bundle-product-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.bundle-product-price {
  color: #28a745;
  font-weight: 600;
  font-size: 0.75rem;
}

.bundle-product-remove {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.bundle-product-remove:hover {
  background: #f8d7da;
  color: #721c24;
}
</style>

<!-- All JavaScript functionality moved to Stimulus controllers -->
